[pytest]
markers =
    protected: marks tests as protected endpoint tests
    render: marks tests as render hosted endpoint tests
    vector: marks tests as tests related the vector db
    caddy: marks tests as tests related the weaviate db caddy proxy
    local: marks tests as local endpoint tests
    health: marks tests related to healthchecks
    embedder: marks the embedder related tests
    documents: marks the document related tests
    shared_secret: marks the document related tests authenticated with shared_secret
    llm: llm related tests

# log_cli = true
# log_cli_level = DEBUG
# log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
# log_cli_date_format = %Y-%m-%d %H:%M:%S