[project]
name = "ava-backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
# Use a stable, supported Python version
requires-python = ">=3.11"

dependencies = [
    "fastapi",
    "pydantic",
    "pydantic-settings",
    "pyjwt",
    "python-decouple",
    "structlog",
    "supabase",
    "uvicorn",
    "protobuf",
    "python-dotenv",
    "qdrant-client",
    "google-genai>=1.29.0",
    "langchain>=0.3.3",
    "langchain-community>=0.3.27",
    "langchain-google-genai>=2.1.9",
    "langchain-google-vertexai>=2.0.28",
    "langchain-openai>=0.3.30",
    "langchainhub>=0.1.21",
    # Use simple, lightweight loaders for a PoC
    "pypdf>=6.0.0",
    "docx2txt>=0.8",
    "pytest>=8.4.2",
    "langchain-qdrant>=0.2.0",
    "black>=25.1.0",
    "python-multipart>=0.0.20",
]

# Use a separate group for development tools
[tool.poetry.group.dev.dependencies]
black = ">=25.1.0"
ipython = ">=9.4.0"
pytest = ">=8.4.1"
rich = ">=14.1.0"
ruff = ">=0.12.10"
