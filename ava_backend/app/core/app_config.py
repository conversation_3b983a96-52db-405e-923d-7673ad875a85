import os
from typing import List, Op<PERSON>, <PERSON><PERSON>, Dict
from functools import lru_cache

from pydantic_settings import BaseSettings, SettingsConfigDict

from app.core.app_logger import get_logger

# ---------------------------------------
applog = get_logger(__name__)


class Settings(BaseSettings):
    model_config = SettingsConfigDict(extra="allow")

    # Document processing settings
    doc_embedding_chunk_size_default: int = 600
    doc_embedding_chunk_overlap_default: int = 50

    @classmethod
    def from_env_file(cls, env_file_path: str):
        if os.path.exists(env_file_path):
            applog.info(f"Loading env file: {env_file_path=}")
            return cls(_env_file=env_file_path)
        else:
            applog.warning(f"Env file not found: {env_file_path=}")
            return cls()


@lru_cache()
def get_settings_from(env_file_path: str = "/etc/secrets/.env") -> Optional[Settings]:
    try:
        applog.info(f"Loading settings from: {env_file_path=}")
        settings = Settings.from_env_file(env_file_path)
        applog.info("Settings loaded successfully")
        return settings
    except Exception as e:
        applog.error(f"Error loading settings: {e}")
        return None
