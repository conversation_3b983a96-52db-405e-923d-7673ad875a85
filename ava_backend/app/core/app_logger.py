import logging
import structlog
from typing import Union

# Configure structlog once globally
_configured = False


def _configure_structlog():
    global _configured
    if not _configured:
        # Configure standard logging first
        logging.basicConfig(
            format="%(message)s",
            level=logging.INFO,
        )

        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.processors.CallsiteParameterAdder(
                    parameters=[
                        structlog.processors.CallsiteParameter.FILENAME,
                        structlog.processors.CallsiteParameter.LINENO,
                    ]
                ),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        _configured = True


def get_logger(
    logger_name: str = "app.main", level: Union[int, str] = logging.INFO
) -> structlog.BoundLogger:
    """Create a logger with specified name and level"""
    _configure_structlog()

    # Convert string levels to int
    if isinstance(level, str):
        level = getattr(logging, level.upper())

    # Get standard logger and set its level
    std_logger = logging.getLogger(logger_name)
    std_logger.setLevel(level)

    # Add handler with custom formatter if not exists
    if not std_logger.handlers:
        handler = logging.StreamHandler()
        handler.setLevel(level)
        formatter = structlog.stdlib.ProcessorFormatter(
            processor=structlog.dev.ConsoleRenderer(colors=True),
        )
        handler.setFormatter(formatter)
        std_logger.addHandler(handler)
        std_logger.propagate = False

    # Return structlog logger
    return structlog.get_logger(logger_name)
