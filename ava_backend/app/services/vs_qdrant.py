from functools import lru_cache
from typing import Optional, List
from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.models import (
    Filter,
    FieldCondition,
    MatchValue,
    PayloadSchemaType,
    Distance,
    VectorParams,
)
from pydantic_settings import SettingsConfigDict
from langchain_qdrant import QdrantVectorStore
from langchain_openai import OpenAIEmbeddings
from langchain.schema import Document

from app.core.app_logger import get_logger

applog = get_logger(logger_name=__name__)



class QdrantVSClient:
    _instance = None
    _client = None

    def __new__(cls, client_settings: SettingsConfigDict):
        if cls._instance is None:
            try:
                cls._instance = super(QdrantVSClient, cls).__new__(cls)
                cls._client = QdrantClient(
                    url=client_settings.qdrant_url,
                    api_key=client_settings.qdrant_api_key,
                )
                # Ensure collection exists with index
                cls._instance.ensure_collection_with_index(
                    client_settings.qdrant_default_collection
                )
            except Exception as e:
                applog.error(f"Error initializing Qdrant client: {e}")
                raise
        return cls._instance

    @property
    def client(self) -> QdrantClient:
        return self._client

    def ensure_collection_with_index(self, collection_name: str):
        """Ensure collection exists and has doc_hash index"""
        try:
            # Check if collection exists
            collections = self._client.get_collections().collections
            collection_exists = any(col.name == collection_name for col in collections)

            if not collection_exists:
                # Create collection with vector configuration
                self._client.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(
                        size=1536, distance=Distance.COSINE
                    ),  # OpenAI embedding size
                )
                applog.info(f"Created collection: {collection_name}")

            # Create index for doc_hash field
            self._client.create_payload_index(
                collection_name=collection_name,
                field_name="metadata.doc_hash",
                field_schema=PayloadSchemaType.KEYWORD,
            )
            
            # Create index for user_id field (required for filtering)
            self._client.create_payload_index(
                collection_name=collection_name,
                field_name="metadata.user_id",
                field_schema=PayloadSchemaType.KEYWORD,
            )
            
            # Create index for document_id field (integer type)
            self._client.create_payload_index(
                collection_name=collection_name,
                field_name="metadata.document_id",
                field_schema=PayloadSchemaType.INTEGER,
            )
            
            # Create index for tags field
            self._client.create_payload_index(
                collection_name=collection_name,
                field_name="metadata.tags",
                field_schema=PayloadSchemaType.KEYWORD,
            )
            
            applog.info(f"Ensured indexes for metadata fields in {collection_name}")
        except Exception as e:
            if "already exists" in str(e).lower() or "index already exists" in str(e).lower():
                applog.info(f"Collection/index already exists: {collection_name}")
            else:
                applog.error(f"Error ensuring collection/index: {e}")

    def delete_by_hash(self, doc_hash: str, collection_name: str):
        """Delete documents with matching hash"""
        try:
            self._client.delete(
                collection_name=collection_name,
                points_selector=Filter(
                    must=[
                        FieldCondition(
                            key="metadata.doc_hash", match=MatchValue(value=doc_hash)
                        )
                    ]
                ),
            )
            applog.info(
                f"Deleted documents with hash {doc_hash} from {collection_name}"
            )
        except Exception as e:
            applog.error(f"Error deleting documents by hash: {e}")

    def add_documents(
        self,
        collection_name: str,
        doc_split: List[Document],
        embedder: OpenAIEmbeddings,
    ):
        """Add documents to Qdrant collection only if hash doesn't exist"""
        if not doc_split or not embedder:
            raise ValueError("Need params: doc_split and embedder.")

        # Ensure collection exists before adding documents
        self.ensure_collection_with_index(collection_name)

        # Check if documents with same hash already exist - skip if they do
        if doc_split:
            doc_hash = doc_split[0].metadata.get("doc_hash")
            if doc_hash and self.hash_exists(doc_hash, collection_name):
                applog.info(f"Documents with hash {doc_hash} already exist, skipping")
                return

        try:
            vector_store = QdrantVectorStore(
                client=self._client, collection_name=collection_name, embedding=embedder
            )
            vector_store.add_documents(doc_split)
        except Exception as e:
            applog.error(f"Error adding documents to Qdrant: {e}")
            raise

    @lru_cache(maxsize=1000)
    def hash_exists(self, doc_hash: str, collection_name: str) -> bool:
        """Check if document hash already exists in Qdrant collection"""
        try:
            result = self._client.scroll(
                collection_name=collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="metadata.doc_hash", match=MatchValue(value=doc_hash)
                        )
                    ]
                ),
                limit=1,
            )
            return len(result[0]) > 0
        except Exception as e:
            applog.error(f"Error checking hash in Qdrant: {e}")
            return False
