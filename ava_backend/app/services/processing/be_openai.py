from langchain_openai import OpenAIEmbeddings
from pydantic_settings import SettingsConfigDict

from app.core.app_logger import get_logger

applog = get_logger(logger_name=__name__)


class OpenAIEmbedder:
    _instance = None
    _embedder = None

    def __new__(cls, client_settings: SettingsConfigDict):
        if cls._instance is None:
            try:
                cls._instance = super(OpenAIEmbedder, cls).__new__(cls)
                cls._embedder = OpenAIEmbeddings(
                    model=client_settings.openai_model_embedding
                )
            except Exception as e:
                applog.error(f"Error initializing OpenAI embedder: {e}")
                raise
        return cls._instance

    @property
    def embedder(self) -> OpenAIEmbeddings:
        return self._embedder
