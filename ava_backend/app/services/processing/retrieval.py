from typing import List, Dict, Any
from os import getenv
from pprint import pp

from dotenv import load_dotenv
import qdrant_client

from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain import hub

from langchain.chains.retrieval import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.chains.history_aware_retriever import create_history_aware_retriever

from qdrant_client import QdrantClient, models
from langchain_qdrant import FastEmbedSparse, QdrantVectorStore, RetrievalMode

from app.utils.custom_decors import timed

from app.core.app_logger import get_logger

# ------------------------------------------------------------------------------------------------------------
applog = get_logger(__name__)

# Load environment variables
load_dotenv()

# Cache these instances at module level since they can be reused across queries
# Add error handling for missing API key

# ------------------------------------------------------------------------------------------------------------
embedding_model = getenv("OPENAI_MODEL_EMBEDDING")
embeddings = OpenAIEmbeddings(model=embedding_model)

qdrant_client = QdrantClient(
    api_key=getenv("QDRANT_API_KEY"), url=getenv("QDRANT_URL"), prefer_grpc=True
)
# Base vector store - will be filtered per user
vs_base = QdrantVectorStore(
    client=qdrant_client, collection_name=getenv('QDRANT_DEFAULT_COLLECTION'), embedding=embeddings
)

llm_chat = ChatOpenAI(model=getenv("OPENAI_MODEL_QUERY"), temperature=0.0)

promtp_qa = hub.pull(getenv("LANGSMITH_PROMPT_RETR_QA"))
stuff_doc_chain = create_stuff_documents_chain(llm=llm_chat, prompt=promtp_qa)

# Prompts
promtp_rephrase = hub.pull(getenv("LANGSMITH_PROMPT_REPHRASE"))

# ------------------------------------------------------------------------------------------------------------
# @timed
def run_llm(
    query: str = "", 
    chat_history: List[Dict[str, Any]] = [], 
    user_id: str = "",
    document_id: str = "",
    tags: List[str] = [],
    debug:bool = False

):
    if not user_id:
        raise ValueError("user_id is required for document retrieval")
    
    # Ensure document_id and tags are mutually exclusive
    if document_id and tags:
        raise ValueError("Cannot specify both document_id and tags - choose one approach")
    
    # Create user-specific retriever with metadata filter
    from qdrant_client.models import Filter, FieldCondition, MatchValue, MatchAny
    
    # Base filter: always filter by user_id
    filter_conditions = [
        FieldCondition(
            key="metadata.user_id",
            match=MatchValue(value=user_id)
        )
    ]
    
    # Add document-specific filter if document_id provided (single document)
    if document_id:
        # Convert document_id to int since it's stored as integer in metadata
        doc_id_int = int(document_id)
        filter_conditions.append(
            FieldCondition(
                key="metadata.document_id",
                match=MatchValue(value=doc_id_int)
            )
        )
    
    # Add tag-based filter if tags provided (multiple documents with tags)
    elif tags:
        filter_conditions.append(
            FieldCondition(
                key="metadata.tags",
                match=MatchAny(any=tags)
            )
        )
    
    if debug:
        applog.info(f"Filtering documents based on: {filter_conditions=}")
    
    user_filter = Filter(must=filter_conditions)
    
    # Create user-filtered retriever
    user_retriever = vs_base.as_retriever(
        search_kwargs={"filter": user_filter}
    )
    
    if debug:
        applog.info(f"Retrieval based on: {user_retriever=}")
    
    # Create history aware retriever for this user
    retr_history = create_history_aware_retriever(
        llm=llm_chat, retriever=user_retriever, prompt=promtp_rephrase
    )

    # applog.info(f"History aware retriever based on: {retr_history=}")
    
    # Create retrieval chain for this user
    chain_qa = create_retrieval_chain(
        retriever=retr_history, combine_docs_chain=stuff_doc_chain
    )
    
    # Run the query with filtered documents
    chain_inputs = {"input": query, "chat_history": chat_history}
    result_full = chain_qa.invoke(input=chain_inputs)
    result = result_full.get("answer", "N/A")
    result_context = result_full.get("context", [])

    # Convert Document objects to serializable format
    serializable_docs = []
    for doc in result_context:
        if hasattr(doc, 'page_content') and hasattr(doc, 'metadata'):
            serializable_docs.append({
                "page_content": doc.page_content,
                "metadata": doc.metadata
            })
        else:
            serializable_docs.append(str(doc))

    new_result = {"query": query, "result": result, "source_documents": serializable_docs}

    if debug:
        applog.info(f"Retrieval result for {query=} based on {serializable_docs=}: {new_result=}")

    return new_result
