from dotenv import load_dotenv
from os import getenv
from pprint import pp
from typing import Optional, Dict, List

import hashlib
import json

from langchain.schema import Document
from langchain_community.document_loaders.text import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter

# OpenAI
from langchain_openai import OpenAIEmbeddings

# Google
from langchain_google_vertexai import VertexAIEmbeddings
import vertexai

from langchain_qdrant import QdrantVectorStore
from langchain_qdrant.qdrant import QdrantVectorStoreError

from app.core.app_logger import get_logger
from app.utils.custom_decors import timed

from .doc_loader import DocumentLoaderService

from . import qdrant_client, embedder

# ----------------------------------------------------------------

applog = get_logger(logger_name=__name__)
load_dotenv()


def create_document_hash(file_path: str, doc_meta: Dict, debug:bool = False) -> str:
    """Create hash from file name and tags for duplicate detection"""
    # Extract file name from path (handles both temp files and original names)
    file_name = doc_meta.get('file_name', file_path.split('/')[-1])
    
    # Only use file name and tags for hash calculation
    hash_data = {
        "file_name": file_name,
        "tags": sorted(doc_meta.get('tags', []))  # Sort tags for consistent ordering
    }

    hv = hashlib.md5(json.dumps(hash_data, sort_keys=True).encode()).hexdigest()
    if debug:        
        print(f"---> Returning Hash value from {hash_data=}: {hv=}")
    return hv


def run_embedder(
    collection_name: str = "",
    file_path: str = "",
    doc_meta: Dict = {},
    debug: bool = False,
) -> str:

    # Check for duplicates first
    # TODO: calc the hash @ the SF and just compare with the DB table there, only call this & update if different
    doc_hash = create_document_hash(file_path, doc_meta, debug=debug) 

    # Skip processing if document already exists
    hash_exists = qdrant_client.hash_exists(doc_hash, collection_name)
    if debug:
        print(f"---> Checking if hash {doc_hash} exists in collection {collection_name}: {hash_exists}")
    
    if hash_exists:
        if debug:
            print(f"---> Document exists, skipping processing")
        return doc_hash

    
    if debug:
        msg = f"---> New processing for {file_path=}"
        print(msg)
        applog.debug(msg)

        msg_meta = f"---> Document metadata:"
        applog.debug(msg_meta)
        pp(doc_meta)

        print(f"Loading DocumentLoaderService")

    dls = DocumentLoaderService()
    pp(dls)

    # Convert Document objects to dictionaries for JSON serialization
    doc_list = []
    try:
        doc_list: List[Document] = dls.load_and_split(file_path, doc_meta)
        # Add hash to each document's metadata
        for doc in doc_list:
            doc.metadata["doc_hash"] = doc_hash
    except Exception as e:
        applog.error(f"Error splitting document: {e}")
        return ""

    if debug:
        msg = f"---> {len(doc_list)} document created."
        print(msg)
        applog.debug(msg)

    if debug:
        msg = f"---> Preparring embedder."
        print(msg)
        applog.debug(msg)

    # load to Qdrant
    if doc_list and embedder:
        if debug:
            msg = f"About to index documents into VectorStore under {collection_name=}."
            print(msg)
            applog.debug(msg)

        try:
            qdrant_client.add_documents(
                collection_name=collection_name,
                doc_split=doc_list,
                embedder=embedder,
            )
        except Exception as e:
            applog.error(f"Error indexing file {file_path=} with {doc_meta=}: {e}")
            return ""

    return doc_hash
