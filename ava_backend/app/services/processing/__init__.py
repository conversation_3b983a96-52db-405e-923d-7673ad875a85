from dotenv import load_dotenv
from os import getenv
from ..vs_qdrant import QdrantVSClient
from .be_openai import OpenAIEmbedder

load_dotenv()


# Create settings objects
class ProcessingSettings:
    def __init__(self):
        self.qdrant_url = getenv("QDRANT_URL")
        self.qdrant_api_key = getenv("QDRANT_API_KEY")
        self.qdrant_default_collection = getenv("QDRANT_DEFAULT_COLLECTION", "documents")
        self.openai_model_embedding = getenv("OPENAI_MODEL_EMBEDDING")
        

settings = ProcessingSettings()

# Package-wide instances
qdrant_client = QdrantVSClient(settings)
openai_embedder = OpenAIEmbedder(settings)
embedder = openai_embedder.embedder
