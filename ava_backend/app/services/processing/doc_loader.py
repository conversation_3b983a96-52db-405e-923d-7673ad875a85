from pathlib import Path
from typing import Dict, List

from httpx import get
from langchain_community.document_loaders.text import TextLoader
from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from app.core.app_logger import get_logger

applog = get_logger(__name__)


# ------------------------------------------------------------------------------------------------------------
class DocumentLoaderService:
    """Service for loading and splitting documents of various types."""

    def __init__(self, chunk_size: int = 600, chunk_overlap: int = 50):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size, chunk_overlap=chunk_overlap
        )
        applog.debug(
            f"DocumentLoaderService initialized with chunk_size={chunk_size} and chunk_overlap={chunk_overlap}."
        )

    def __str__(self) -> str:
        return f"DocumentLoaderService <{self.chunk_size=}, {self.chunk_overlap=}>"

    def __repr__(self) -> str:
        return self.__str__()

    def _get_loader(self, doc_path: Path):
        """Get appropriate loader based on file extension."""
        suffix = doc_path.suffix.lower()

        if suffix == ".pdf":
            msg = "Using PyPDFLoader"
            print(msg)
            applog.info(msg)

            return PyPDFLoader(str(doc_path))
        elif suffix == ".docx":

            msg = "Using Docx2txtLoader"
            print(msg)
            applog.info(msg)
            
            return Docx2txtLoader(str(doc_path))
        elif suffix in [".txt", ".md"]:
            
            msg = "Using TextLoader"
            print(msg)
            applog.info(msg)

            return TextLoader(file_path=doc_path, autodetect_encoding=True)
        else:
            raise ValueError(f"Unsupported file type: {suffix}")

    def load_docs(self, doc_path: str, doc_meta: Dict = {}) -> List[Document]:
        """Load documents from file path with metadata."""
        path = Path(doc_path)
        loader = self._get_loader(path)

        try:
            documents = loader.load()
            # Add metadata to documents
            for doc in documents:
                doc.metadata.update(**doc_meta)

            applog.debug(f"Documents loaded into list of {len(documents)}.")
            return documents
        except Exception as e:
            applog.error(f"Error loading documents: {e}")
            raise

    def split_docs(self, raw_documents: List[Document]) -> List[Document]:
        """Split documents into chunks."""
        try:
            docs_split = self.splitter.split_documents(raw_documents)
            applog.debug(
                f"Raw docs split into {len(docs_split)} chunks using "
                f"chunk_size={self.chunk_size} and chunk_overlap={self.chunk_overlap}."
            )
            return docs_split
        except Exception as e:
            applog.error(f"Error splitting documents: {e}")
            raise

    def load_and_split(self, doc_path: str, doc_meta: Dict = {}) -> List[Document]:        
        """Load and split documents in one call."""
        documents = self.load_docs(doc_path, doc_meta)
        return self.split_docs(documents)

