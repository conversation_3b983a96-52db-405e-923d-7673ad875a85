import os
from typing import Dict
from pydantic_settings import SettingsConfigDict
from supabase import create_client, Client

from app.core.app_logger import get_logger

#  ---------------------------------------

applog = get_logger(__name__)


class SupabaseClient:
    _instance = None
    _client = None

    def __new__(cls, client_settings: SettingsConfigDict):
        if cls._instance is None:
            try:
                cls._instance = super(SupabaseClient, cls).__new__(cls)
                cls._client = create_client(
                    client_settings.supabase_url, client_settings.supabase_anonkey
                )
            except Exception as e:
                applog.error(f"Error initializing Supabase client: {e}")
                raise

        return cls._instance

    @property
    def client(self) -> Client:
        return self._client
