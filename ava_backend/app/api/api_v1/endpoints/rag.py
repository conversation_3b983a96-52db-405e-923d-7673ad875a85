from pydantic import <PERSON>Mode<PERSON>
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import JSONResponse

from app.core.app_logger import get_logger
from app.api.api_v1.endpoints import auth
from app.services.processing.retrieval import run_llm

# ------------------------------------------------------------------------------------------------------------
applog = get_logger(__name__)
router = APIRouter(tags=["rag"])

# ------------------------------------------------------------------------------------------------------------


class DocumentQuestionRequest(BaseModel):
    question: str
    chat_history: List[Dict[str, Any]] = []
    document_id: Optional[str] = None
    tags: Optional[List[str]] = None

# ------------------------------------------------------------------------------------------------------------
@router.post("/ask")
async def ask_about_documents(
    request: Request, 
    query: DocumentQuestionRequest,
    user: dict = Depends(auth.user_logged_in)
):
    """Ask questions about user's documents - specific document or by tags"""
    
    applog.info(
        f"User {user.id} asking: {query.question!r} about document_id={query.document_id}, tags={query.tags}"
    )

    try:
        result = run_llm(
            query=query.question,
            chat_history=query.chat_history,
            user_id=user.id,
            document_id=query.document_id,
            tags=query.tags
        )
        
        applog.info(f"RAG response generated for user {user.id}: {result}")
        return JSONResponse(content=result, status_code=200)
        
    except Exception as e:
        applog.error(f"Error in document question: {e}")
        raise HTTPException(status_code=500, detail=str(e))