import requests
import logging
import os
from typing import List, Optional
from fastapi import APIR<PERSON><PERSON>, Depends, HTTPEx<PERSON>, Request
from fastapi.responses import JSONResponse

from app.api.api_v1.endpoints import auth
from app.core.app_logger import get_logger

# ------------------------------------------------------------------------------------------------

applog = get_logger(logger_name=__name__, level=logging.DEBUG)

router = APIRouter()


# ------------------------------------------------------------------------------------------------
@router.get("/")
async def protected_fetch_healthcheck(key: str, request: Request):
    """
    This ep reaches out to a key saved into the environment vars of Render
    Called as EPURL/health?key=...
    """

    if key != request.app.state.settings.render_health_api_key:
        raise HTTPException(status_code=401, detail="Unauthorized healthcheck.")

    return JSONResponse(content={"message": "OK"}, status_code=200)


# ------------------------------------------------------------------------------------------------
# TODO: redesign for qdrant
# @router.get("/weaviate")
# async def health_check_weaviate_authorized(
#     request: Request, user: dict = Depends(auth.user_logged_in)
# ):
#     """
#     Testing against the SPB protected documents endpoint first and then
#     sending authorized req. to the Weaviate endpoint with basic auth (set in the Caddyfile).
#     """

#     response = None
#     try:
#         auth_wv8_ep = (
#             request.app.state.settings.wv8_user,
#             request.app.state.settings.wv8_pass,
#         )

#         response = requests.get("https://ava-weaviate.fly.dev/", auth=auth_wv8_ep)

#     except Exception as e:
#         applog.error(f"Error in test_healthcheck_weaviate_from_local: {e}")
#     else:
#         applog.info(f"{response.content=}, {response.status_code=}")
#     finally:
#         if response is None:
#             response = JSONResponse(content={"response": "NO RESP"}, status_code=401)
#         else:
#             return JSONResponse(
#                 content={"response": response.content.decode()},
#                 status_code=response.status_code,
#             )


# # ------------------------------------------------------------------------------------------------
# @router.get("/postgres")
# async def health_check_cretate_postgres_client_object():
#     applog.debug("Checking postgres")
#     # TODO: implement
#     return JSONResponse(content={"message": "NOT IMPLEMENTED"}, status_code=200)
