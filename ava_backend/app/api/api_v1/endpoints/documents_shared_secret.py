from pprint import pp
from pydantic import BaseModel, Field
from typing import Dict, List
from fastapi import APIRouter, Request, Depends, HTTPException, File, UploadFile, Form
from fastapi.responses import JSONResponse

import tempfile
import json
import os

from app.core.app_logger import get_logger
from app.api.api_v1.endpoints import auth

from app.services.processing.ingestion import run_embedder
from app.services.processing import qdrant_client


# ------------------------------------------------------------------------------------------------------------
class EmbedDocumentSSRequest(BaseModel):
    collection_name: str
    doc_meta: Dict = Field(default_factory=dict)
    debug: bool = False
    service_key: str

class DeleteDocumentSSRequest(BaseModel):
    collection_name: str
    doc_hash: str
    service_key: str



# ------------------------------------------------------------------------------------------------------------
applog = get_logger(__name__)
router = APIRouter()

# ------------------------------------------------------------------------------------------------------------

@router.post("/embed_ss")
async def protected_shared_secret_embed_document(
    request: Request,
    file: UploadFile = File(...),
    collection_name: str = Form(...),
    doc_meta: str = Form(...),
    debug: bool = Form(False),
    service_key: str = Form(...),
):
    """
    Endpoint to process the embedding request. 
    Uses shared secret authentication
    """
    # Service-to-service authentication
    if service_key != request.app.state.settings.render_supabase_func_api_key:
        raise HTTPException(status_code=401, detail="Unauthorized service call")
    
    try:
        # Parse doc_meta JSON string
        doc_meta_dict = json.loads(doc_meta)
        doc_meta_dict["file_name"] = file.filename
        doc_meta_dict["file_type"] = file.filename.split(".")[-1] if "." in file.filename else ""
        
        applog.info(f"Service processing file: {file.filename} with {doc_meta_dict=}")
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Process the file
            doc_hash = run_embedder(
                collection_name=collection_name,
                file_path=temp_file_path,
                doc_meta=doc_meta_dict,
                debug=debug,
            )
            
            return JSONResponse(content={"doc_hash": doc_hash}, status_code=200)
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid doc_meta JSON")
    except Exception as e:
        applog.error(f"Error processing service file upload: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/delete_ss")
async def protected_shared_secret_delete_document(
    request: Request,
    delete_request: DeleteDocumentSSRequest,
):
    """Delete endpoint using shared secret authentication"""
    # Service-to-service authentication
    if delete_request.service_key != request.app.state.settings.render_supabase_func_api_key:
        raise HTTPException(status_code=401, detail="Unauthorized service call")
    
    qdrant_client.delete_by_hash(
        delete_request.doc_hash, delete_request.collection_name
    )
    return JSONResponse(content={"deleted": True}, status_code=200)
