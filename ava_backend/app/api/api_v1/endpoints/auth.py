import requests
import os
from typing import List, Optional

from fastapi import API<PERSON><PERSON>er, HTTPException, Depends, status, Header, Request
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse

from pydantic import BaseModel

from app.core.app_logger import get_logger

# ------------------------------------------------------------------------------------------------
applog = get_logger(__name__)

router = APIRouter()
security = HTTPBearer()
blacklisted_tokens = set()


# ------------------------------------------------------------------------------------------------
# Auth EPs
class SignupRequest(BaseModel):
    email: str
    password: str


@router.post("/signup")
async def sign_up(request: Request, signup_request: SignupRequest):
    try:
        resp_signup = request.app.state.sclient.auth.sign_up(
            {"email": signup_request.email, "password": signup_request.password}
        )
        # TODO: JsonResp
        return {
            "message": "Signup successful",
            "access_token": (
                resp_signup.session.access_token if resp_signup.session else None
            ),
            "user": resp_signup.user.id if resp_signup.user else None,
        }
    except Exception as e:
        applog.error(f"Signup error: {e}")
        # TODO: JsonResp
        return {"error": str(e)}


#  ---------------------------------------
class LoginRequest(BaseModel):
    email: str
    password: str


@router.post("/login")
async def login(request: Request, login_request: LoginRequest):
    try:
        resp_login = request.app.state.sclient.auth.sign_in_with_password(
            {"email": login_request.email, "password": login_request.password}
        )
        # TODO: JsonResp
        return {
            "message": "Login successful",
            "access_token": (
                resp_login.session.access_token if resp_login.session else None
            ),
            "refresh_token": (
                resp_login.session.refresh_token if resp_login.session else None
            ),
            "user": resp_login.user.id if resp_login.user else None,
        }
    except Exception as e:
        applog.error(f"Login error: {e}")
        # TODO: JsonResp
        return {"error": str(e)}


#  ---------------------------------------
class RefreshRequest(BaseModel):
    refresh_token: str


@router.post("/refresh")
async def refresh_token(request: Request, request_refresh: RefreshRequest):
    try:
        resp = request.app.state.sclient.auth.refresh_session(
            request_refresh.refresh_token
        )
        # TODO: JsonResp
        return {
            "access_token": resp.session.access_token,
            "refresh_token": resp.session.refresh_token,
        }
    except Exception as e:
        applog.error(f"Refresh error: {e}")
        # TODO: JsonResp
        return {"error": "Invalid refresh token"}


#  ---------------------------------------
@router.post("/logout")
async def sign_out(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        blacklisted_tokens.add(credentials.credentials)
        # TODO: JsonResp
        return {"message": "Logout successful"}
    except Exception as e:
        applog.error(f"Logout error: {e}")
        # TODO: JsonResp
        return {"error": "Logout failed"}


#  ---------------------------------------
async def user_logged_in(
    request: Request, credentials: HTTPAuthorizationCredentials = Depends(security)
):
    try:
        # Check if token is blacklisted (immediate logout)
        if credentials.credentials in blacklisted_tokens:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has been revoked",
            )

        # Verify token with Supabase
        user = request.app.state.sclient.auth.get_user(credentials.credentials)
        if not user or not user.user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token"
            )
        return user.user
    except Exception as e:
        print(f"Auth error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication failed"
        )

    # ------------------------------------------------------------------------------------------------
    # @router.get("/")
    # async def protected_fetch_healthcheck(key: str = None):
    #     """
    #     This ep reaches out to a key saved into the environment vars of Render
    #     Called as EPURL/health?key=...
    #     """
    #     applog.debug("Checking '/'")
    #     if key != HEALTH_API_KEY:
    #         raise HTTPException(status_code=401, detail="Unauthorized healthcheck.")
    return JSONResponse(content={"message": "OK"}, status_code=200)


# ------------------------------------------------------------------------------------------------
# @router.get("/weaviate/client")
# async def health_check_create_wv8_client_object():
#     resp = "OK"
#     resp_code = 200

#     applog.debug("Checking weaviate")
#     vs = get_vector_store()
#     if not vs:
#         resp = "NOT OK"
#         resp_code = 500
#     applog.debug(f"Weaviate client: {vs=}")

#     return JSONResponse(content={"message": resp}, status_code=resp_code)

# # ------------------------------------------------------------------------------------------------
# @router.get("/postgres")
# async def health_check_cretate_postgres_client_object():
#     applog.debug("Checking postgres")
#     # TODO: implement
#     return JSONResponse(content={"message": "NOT IMPLEMENTED"}, status_code=200)
