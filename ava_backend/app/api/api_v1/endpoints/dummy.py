from pprint import pp
from pydantic import BaseModel, Field
from typing import Dict, List
from fastapi import APIRouter, Request, Depends, HTTPException, File, UploadFile, Form
from fastapi.responses import JSONResponse

import tempfile
import json
import os

from app.core.app_logger import get_logger
from app.api.api_v1.endpoints import auth

from app.services.processing.ingestion import run_embedder
from app.services.processing import qdrant_client


# ------------------------------------------------------------------------------------------------------------
applog = get_logger(__name__)
router = APIRouter()
# ------------------------------------------------------------------------------------------------------------


@router.get("/dummy")
async def protected_fetch_dummy_documents(
    request: Request, user: dict = Depends(auth.user_logged_in)
):
    resp_items = {
        "items": [{"id": 1, "name": "Contract 1"}, {"id": 2, "name": "Contract 2"}]
    }

    applog.debug(f"Fetching {resp_items=}")

    return JSONResponse(content=resp_items, status_code=200)