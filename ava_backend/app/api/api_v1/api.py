from fastapi import APIRouter

from app.api.api_v1.endpoints import (
    healthchecks,
    auth,
    rag,
)
from app.api.api_v1.endpoints import documents_auth, documents_shared_secret, dummy

api_router = APIRouter()

api_router.include_router(healthchecks.router, prefix="/health", tags=["healthchecks"])
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])

api_router.include_router(documents_auth.router, prefix="/documents", tags=["documents", "dummy"])
api_router.include_router(dummy.router, prefix="/documents", tags=["documents", "dummy"])
api_router.include_router(documents_shared_secret.router, prefix="/documents", tags=["documents", "shared secret"])

api_router.include_router(rag.router, prefix="/rag", tags=["rag"])
