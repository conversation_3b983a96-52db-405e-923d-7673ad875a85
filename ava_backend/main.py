# import warnings
# warnings.filterwarnings("ignore", message=".*Protobuf gencode version.*")

import jwt
import os
import json

from fastapi import FastAPI, HTTPException, Depends, status, Header, Request
from fastapi.security import (
    HTTPBearer,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON>esponse

from contextlib import asynccontextmanager

from typing import Optional
from typing import AsyncGenerator

from dotenv import load_dotenv


# Local imports
from app.services.be_supa import SupabaseClient

# from app.services.vs_qdrant import QdrantClient
from app.core.app_logger import get_logger
from app.core.app_config import get_settings_from

from app.api.api_v1 import api
from app.api.api_v1.endpoints import auth

#  ---------------------------------------
# Load environment variables
load_dotenv()

# Load add logger
applog = get_logger(__name__)

# Also create & load settings from .env
# If running on Render
if os.environ.get("RUNNING_ON_RENDER") == "True":
    env_file_location = "/etc/secrets/.env"
    gcp_key_file_location = "/etc/secrets/ava-gcp-468910-f5305fc97c5e.json"
else:
    # If running locally
    env_file_location = os.path.join(os.path.dirname(os.path.abspath(__file__)), ".env")
    gcp_key_file_location = os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
        "keys",
        "ava-gcp-468910-f5305fc97c5e.json",
    )
# Create settings inst.
settings = get_settings_from(env_file_path=env_file_location)

# Create bearer inst.
security = HTTPBearer()


# -------------------------------------------------------------------------------------
@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator:

    applog.info(f"Creating service objects into app.state.")

    try:
        applog.info("Load settings")
        app.state.settings = settings

        applog.info("Init Supabase Client")
        app.state.sclient = SupabaseClient(client_settings=settings).client
        # app.state.qclient = QdrantClient()

    except Exception as e:
        err_msg = f"Error initializing Supabase: {e}"
        applog.error(err_msg)
        raise RuntimeError(err_msg) from e
    else:
        applog.info(
            f"Supabase Client initialized successfully & added to app.state: {app.state.sclient=}"
        )

    #  TODO
    # app.state.db = get_postgres()
    # -----
    yield
    # -----
    applog.info(f"Shutting down {settings.app_name}")

    # TODO: close objects in app.state
    # applog.debug(f"Closing service objects from app.state.")
    # TODO postgres
    # app.state.db.close()


fapp = FastAPI(
    title=settings.app_name,
    description=settings.app_description,
    version="0.1.0",
    lifespan=lifespan,
    # docs_url="/docs",
    # openapi_url="/openapi.json"
)

# Set up CORS
fapp.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development only, restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# -------------------------------------------------------------------------------------
# Root and health
from app.api.api_v1.endpoints import auth


@fapp.get("/")
async def prfetch_root(user: Optional[dict] = Depends(auth.user_logged_in)):
    # add json resp
    return {"message": f"Hello authenticated {user=}"}


fapp.include_router(api.api_router, prefix="/api/v1", tags=["api" "v1"])
