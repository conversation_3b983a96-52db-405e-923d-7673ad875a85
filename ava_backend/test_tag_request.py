import requests
import json

# Test request using tags
def test_tag_based_question():
    url = "http://localhost:8000/api/v1/rag/ask"
    
    # You'll need a valid auth token
    headers = {
        "Authorization": "Bearer YOUR_AUTH_TOKEN_HERE",
        "Content-Type": "application/json"
    }
    
    # Request asking about documents with specific tags
    payload = {
        "question": "What important contracts mention payment terms?",
        "tags": ["important", "contract"],
        "chat_history": []
    }
    
    response = requests.post(url, headers=headers, json=payload)
    
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")

if __name__ == "__main__":
    test_tag_based_question()