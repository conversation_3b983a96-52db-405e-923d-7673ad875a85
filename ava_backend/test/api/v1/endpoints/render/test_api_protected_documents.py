import pytest
import requests
from app.core.app_logger import get_logger

applog = get_logger(logger_name=__name__)

# @pytest.mark.skip()
@pytest.mark.protected
@pytest.mark.render
def test_remote_documents_endpoint_dummy(settings, auth_token):
    """Testing against the SPB protected documents endpoint"""
    render_url = settings.render_api_url
    render_auth_headers = {"Authorization": f"Bearer {auth_token}"}

    response = requests.get(
        f"{render_url}/api/v1/documents/dummy", headers=render_auth_headers
    )
    assert response.status_code == 200

    data = response.json()
    assert "items" in data
    assert len(data["items"]) == 2
    assert data["items"][0]["name"] == "Contract 1"
    assert data["items"][1]["name"] == "Contract 2"


# @pytest.mark.skip()
@pytest.mark.embedder
@pytest.mark.render
@pytest.mark.parametrize(
    "file_path,tags",
    [
        ("test/data_sources/sample_sk.txt", ["important", "car"]),
        ("test/data_sources/job.pdf", ["important", "car"]),
        ("test/data_sources/ideas.docx", ["car"]),
    ],
)
def test_remote_embed_document(settings, auth_token, file_path, tags):
    """Test /embed endpoint with authentication"""
    import json

    render_url = settings.render_api_url
    headers = {"Authorization": f"Bearer {auth_token}"}

    doc_meta = {
        "tags": tags,
        "user_id": "test_user",
        "document_id": 999
    }

    files = {
        'file': (file_path.split('/')[-1], open(file_path, 'rb'), 'application/octet-stream')
    }
    
    form_data = {
        'collection_name': 'documents',
        'doc_meta': json.dumps(doc_meta),
        'debug': 'true'
    }

    response = requests.post(
        f"{render_url}/api/v1/documents/embed", headers=headers, files=files, data=form_data
    )
    
    files['file'][1].close()  # Close the file

    data = response.json()
    applog.info(f"Response: {data}")
    assert response.status_code == 200


# @pytest.mark.skip()
@pytest.mark.embedder
@pytest.mark.render
def test_remote_delete_document(settings, auth_token):
    """Test /delete endpoint with authentication"""

    render_url = settings.render_api_url
    headers = {"Authorization": f"Bearer {auth_token}"}
    
    # First embed a document to have something to delete
    import json
    
    doc_meta = {
        "tags": ["test", "delete"],
        "user_id": "test_user",
        "document_id": 999
    }
    
    files = {
        'file': ('sample_sk.txt', open('test/data_sources/sample_sk.txt', 'rb'), 'text/plain')
    }
    
    form_data = {
        'collection_name': 'documents',
        'doc_meta': json.dumps(doc_meta),
        'debug': 'true'
    }
    
    embed_response = requests.post(f"{render_url}/api/v1/documents/embed", headers=headers, files=files, data=form_data)
    files['file'][1].close()  # Close the file
    embed_data_response = embed_response.json()
    doc_hash = embed_data_response["doc_hash"]

    delete_data = {
        "collection_name": "documents",
        "doc_hash": doc_hash
    }

    response = requests.delete(f"{render_url}/api/v1/documents/delete", headers=headers, json=delete_data)

    data = response.json()
    applog.info(f"Response: {data}")
    assert response.status_code == 200
    assert data["deleted"] == True


# @pytest.mark.skip()
@pytest.mark.embedder
@pytest.mark.render
def test_remote_document_lifecycle(settings, auth_token):
    """Test document creation, update, and deletion lifecycle"""

    render_url = settings.render_api_url
    headers = {"Authorization": f"Bearer {auth_token}"}
    
    # 1. Create document with unique tags to avoid conflicts
    import json
    
    doc_meta = {
        "tags": ["remote_lifecycle_tag1", "remote_lifecycle_tag2"],
        "user_id": "test_user",
        "document_id": 999
    }
    
    files = {
        'file': ('sample_sk.txt', open('test/data_sources/sample_sk.txt', 'rb'), 'text/plain')
    }
    
    form_data = {
        'collection_name': 'documents',
        'doc_meta': json.dumps(doc_meta),
        'debug': 'true'
    }
    
    response1 = requests.post(f"{render_url}/api/v1/documents/embed", headers=headers, files=files, data=form_data)
    data1 = response1.json()
    print(f"====================> {data1=}")
    
    # Close the file
    files['file'][1].close()
    doc_hash1 = data1["doc_hash"]
    applog.info(f"1. Created document with hash: {doc_hash1}")
    assert response1.status_code == 200
    
    # 2. Delete the old document before creating updated version
    delete_old = {"collection_name": "documents", "doc_hash": doc_hash1}
    delete_response = requests.delete(f"{render_url}/api/v1/documents/delete", headers=headers, json=delete_old)
    applog.info(f"2. Deleted old document: {delete_response.json()}")
    
    # 3. Create updated document with changed tags
    doc_meta_updated = {
        "tags": ["remote_lifecycle_tag1", "remote_lifecycle_tag3"],  # Changed tag2 to tag3
        "user_id": "test_user",
        "document_id": 999
    }
    
    files2 = {
        'file': ('sample_sk.txt', open('test/data_sources/sample_sk.txt', 'rb'), 'text/plain')
    }
    
    form_data2 = {
        'collection_name': 'documents',
        'doc_meta': json.dumps(doc_meta_updated),
        'debug': 'true'
    }
    
    response2 = requests.post(f"{render_url}/api/v1/documents/embed", headers=headers, files=files2, data=form_data2)
    files2['file'][1].close()
    data2 = response2.json()
    doc_hash2 = data2["doc_hash"]
    applog.info(f"3. Created updated document with hash: {doc_hash2}")
    assert response2.status_code == 200
    assert doc_hash1 != doc_hash2  # Hash should be different due to tag change
    
    # 4. Delete the final document
    delete_final = {"collection_name": "documents", "doc_hash": doc_hash2}
    response3 = requests.delete(f"{render_url}/api/v1/documents/delete", headers=headers, json=delete_final)
    data3 = response3.json()
    applog.info(f"4. Deleted final document: {data3}")
    assert response3.status_code == 200
    assert data3["deleted"] == True
    
    applog.info("Remote test completed - collection should be empty of test documents")


@pytest.mark.embedder
@pytest.mark.render
@pytest.mark.shared_secret
def test_remote_document_lifecycle_shared_secret(settings):
    """Test document creation, update, and deletion lifecycle using shared secret"""

    render_url = settings.render_api_url
    # No auth headers - using shared secret instead
    
    # 1. Create document with unique tags to avoid conflicts
    import json
    
    doc_meta = {
        "tags": ["ss_lifecycle_tag1", "ss_lifecycle_tag2"],
        "user_id": "test_user_ss",
        "document_id": 888
    }
    
    files = {
        'file': ('sample_sk.txt', open('test/data_sources/sample_sk.txt', 'rb'), 'text/plain')
    }
    
    form_data = {
        'collection_name': 'documents',
        'doc_meta': json.dumps(doc_meta),
        'debug': 'true',
        'service_key': settings.render_supabase_func_api_key
    }
    
    response1 = requests.post(f"{render_url}/api/v1/documents/embed_ss", files=files, data=form_data)
    data1 = response1.json()
    print(f"====================> SS {data1=}")
    
    # Close the file
    files['file'][1].close()
    doc_hash1 = data1["doc_hash"]
    applog.info(f"1. SS Created document with hash: {doc_hash1}")
    assert response1.status_code == 200
    
    # 2. Delete the old document before creating updated version
    delete_old = {
        "collection_name": "documents", 
        "doc_hash": doc_hash1,
        "service_key": settings.render_supabase_func_api_key
    }
    delete_response = requests.delete(f"{render_url}/api/v1/documents/delete_ss", json=delete_old)
    applog.info(f"2. SS Deleted old document: {delete_response.json()}")
    
    # 3. Create updated document with changed tags
    doc_meta_updated = {
        "tags": ["ss_lifecycle_tag1", "ss_lifecycle_tag3"],  # Changed tag2 to tag3
        "user_id": "test_user_ss",
        "document_id": 888
    }
    
    files2 = {
        'file': ('sample_sk.txt', open('test/data_sources/sample_sk.txt', 'rb'), 'text/plain')
    }
    
    form_data2 = {
        'collection_name': 'documents',
        'doc_meta': json.dumps(doc_meta_updated),
        'debug': 'true',
        'service_key': settings.render_supabase_func_api_key
    }
    
    response2 = requests.post(f"{render_url}/api/v1/documents/embed_ss", files=files2, data=form_data2)
    files2['file'][1].close()
    data2 = response2.json()
    doc_hash2 = data2["doc_hash"]
    applog.info(f"3. SS Created updated document with hash: {doc_hash2}")
    assert response2.status_code == 200
    assert doc_hash1 != doc_hash2  # Hash should be different due to tag change
    
    # 4. Delete the final document
    delete_final = {
        "collection_name": "documents", 
        "doc_hash": doc_hash2,
        "service_key": settings.render_supabase_func_api_key
    }
    response3 = requests.delete(f"{render_url}/api/v1/documents/delete_ss", json=delete_final)
    data3 = response3.json()
    applog.info(f"4. SS Deleted final document: {data3}")
    assert response3.status_code == 200
    assert data3["deleted"] == True
    
    applog.info("SS Remote test completed - collection should be empty of test documents")