import pytest
import os
import requests


@pytest.mark.render
def test_healthcheck_endpoint_render_OK(settings):
    # Test that healthcheck endpoint returns 200 status code with correct key
    response = requests.get(
        f"{settings.render_api_url}/api/v1/health/?key={settings.render_health_api_key}"
    )
    assert response.status_code == 200
    assert response.json() == {"message": "OK"}


@pytest.mark.render
def test_healthcheck_endpoint_render_no_key(settings):
    # Test that healthcheck endpoint returns 401 without key
    response = requests.get(f"{settings.render_api_url}/api/v1/health/")
    assert response.status_code == 422


@pytest.mark.render
def test_healthcheck_endpoint_render_wrong_key(settings):
    # Test that healthcheck endpoint returns 401 with wrong key
    response = requests.get(f"{settings.render_api_url}/api/v1/health/?key=wrong_key")
    assert response.status_code == 401


@pytest.mark.render
def test_healthcheck_endpoint_render_wrong_url(settings):
    # Test that healthcheck endpoint returns 404 with wrong url
    response = requests.get(
        f"{settings.render_api_url}/ap/v1/health/?key={settings.render_health_api_key}"
    )
    assert response.status_code == 404
