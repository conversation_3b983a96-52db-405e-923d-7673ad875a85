import pytest
from main import fapp

from app.core.app_logger import get_logger

applog = get_logger(logger_name=__name__)

# ------------------------------------------------------------------------------------------------------------
# @pytest.mark.skip()
@pytest.mark.dummy
@pytest.mark.local
@pytest.mark.documents
def test_fetch_dummy_documents_with_auth(local_client, auth_token):
    """Test /dummy endpoint with Supabase authentication"""

    headers = {"Authorization": f"Bearer {auth_token}"}
    response = local_client.get("/api/v1/documents/dummy", headers=headers)

    assert response.status_code == 200

    data = response.json()

    assert "items" in data
    assert len(data["items"]) == 2
    assert data["items"][0]["id"] == 1
    assert data["items"][0]["name"] == "Contract 1"
    assert data["items"][1]["id"] == 2
    assert data["items"][1]["name"] == "Contract 2"

# ------------------------------------------------------------------------------------------------------------
# pytest.mark.skip()
@pytest.mark.dummy
@pytest.mark.local
@pytest.mark.documents
@pytest.mark.xfail()
def test_fetch_dummy_documents_without_auth(local_client):
    """Test /dummy endpoint without authentication should fail"""

    response = local_client.get("/api/v1/documents/dummy")
    assert response.status_code == 401