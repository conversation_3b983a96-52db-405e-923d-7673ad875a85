import pytest
from fastapi.testclient import TestClient
from main import fapp

from app.core.app_logger import get_logger

applog = get_logger(logger_name=__name__)


@pytest.mark.local
@pytest.mark.health
def test_healthcheck_endpoint_local_with_key(local_client):

    # Test that healthcheck endpoint returns 200 status code with correct key
    response = local_client.get(
        f"/api/v1/health/?key={fapp.state.settings.render_health_api_key}"
    )
    assert response.status_code == 200
    # assert response.json() == {"message": "OK"}


@pytest.mark.local
@pytest.mark.health
def test_healthcheck_endpoint_local_with_no_key(local_client):

    # Test that healthcheck endpoint returns 401 without key
    response = local_client.get("/api/v1/health/")
    assert response.status_code == 422
