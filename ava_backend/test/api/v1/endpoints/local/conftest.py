import pytest
import requests
import os

from fastapi.testclient import <PERSON><PERSON>lient

from main import fapp
from app.services.be_supa import <PERSON>pabaseClient

from app.core.app_logger import get_logger

applog = get_logger(logger_name=__name__)


@pytest.fixture(scope="package")
def local_client(settings):
    # Set up app state for testing
    fapp.state.settings = settings

    # Initialize Supabase client (normally done in lifespan)
    fapp.state.sclient = SupabaseClient(client_settings=settings).client

    if settings is None:
        raise RuntimeError("Failed to load settings for testing")

    tc = TestClient(fapp)
    applog.info(f"local test client created with settings: {tc=}")

    return tc
