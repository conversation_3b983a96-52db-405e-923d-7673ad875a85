import pytest
from main import fapp

from app.core.app_logger import get_logger
applog = get_logger(logger_name=__name__)

# ------------------------------------------------------------------------------------------------------------
# @pytest.mark.skip()
@pytest.mark.embedder
@pytest.mark.local
@pytest.mark.parametrize(
    "file_path,tags",
    [
        ("test/data_sources/sample_sk.txt", ["important", "car"]),
        # ("test/data_sources/job.pdf", ["important", "car"]),
        # ("test/data_sources/ideas.docx", ["car"]),
    ],
)
def test_embed_document(local_client, auth_token, file_path, tags):
    """Test /embed endpoint with authentication"""
    import json

    headers = {"Authorization": f"Bearer {auth_token}"}

    doc_meta = {
        "tags": tags,
        "user_id": "test_user",
        "document_id": 999
    }

    with open(file_path, 'rb') as f:
        files = {'file': (file_path.split('/')[-1], f, 'text/plain')}
        data = {
            'collection_name': 'documents',
            'doc_meta': json.dumps(doc_meta),
            'debug': 'true'
        }
        
        response = local_client.post(
            "/api/v1/documents/embed", headers=headers, files=files, data=data
        )

    response_data = response.json()
    applog.info(f"Response: {response_data}")
    assert response.status_code == 200

# ------------------------------------------------------------------------------------------------------------
# @pytest.mark.skip()
@pytest.mark.embedder
@pytest.mark.local
def test_create_and_delete_document(local_client, auth_token):
    """Test /delete endpoint with authentication"""

    headers = {"Authorization": f"Bearer {auth_token}"}
    
    # First embed a document to have something to delete
    import json
    
    doc_meta = {
        "tags": ["test", "delete"],
        "user_id": "test_user",
        "document_id": 999
    }
    
    with open("test/data_sources/sample_sk.txt", 'rb') as f:
        files = {'file': ('sample_sk.txt', f, 'text/plain')}
        data = {
            'collection_name': 'documents',
            'doc_meta': json.dumps(doc_meta),
            'debug': 'true'
        }
        
        embed_response = local_client.post("/api/v1/documents/embed", headers=headers, files=files, data=data)
    embed_data_response = embed_response.json()
    doc_hash = embed_data_response["doc_hash"]
    
    # Now delete the document using the returned hash
    delete_data = {
        "collection_name": "documents",
        "doc_hash": doc_hash
    }

    response = local_client.request("DELETE", "/api/v1/documents/delete", headers=headers, json=delete_data)

    data = response.json()
    applog.info(f"Response: {data}")
    assert response.status_code == 200
    assert data["deleted"] == True

# ------------------------------------------------------------------------------------------------------------
@pytest.mark.embedder
@pytest.mark.local
def test_document_lifecycle(local_client, auth_token):
    """Test document creation, update, and deletion lifecycle"""

    headers = {"Authorization": f"Bearer {auth_token}"}
    
    # 1. Create document with unique tags to avoid conflicts
    import json
    
    doc_meta = {
        "tags": ["lifecycle_tag1", "lifecycle_tag2"],
        "user_id": "test_user",
        "document_id": 999
    }
    
    with open("test/data_sources/sample_sk.txt", 'rb') as f:
        files = {'file': ('sample_sk.txt', f, 'text/plain')}
        data = {
            'collection_name': 'documents',
            'doc_meta': json.dumps(doc_meta),
            'debug': 'true'
        }
        
        response1 = local_client.post("/api/v1/documents/embed", headers=headers, files=files, data=data)
    data1 = response1.json()
    doc_hash1 = data1["doc_hash"]
    applog.info(f"1. Created document with hash: {doc_hash1}")
    assert response1.status_code == 200
    
    # 2. Delete the old document before creating updated version
    delete_old = {"collection_name": "documents", "doc_hash": doc_hash1}
    delete_response = local_client.request("DELETE", "/api/v1/documents/delete", headers=headers, json=delete_old)
    applog.info(f"2. Deleted old document: {delete_response.json()}")
    
    # 3. Create updated document with changed tags
    doc_meta_updated = {
        "tags": ["lifecycle_tag1", "lifecycle_tag3"],  # Changed tag2 to tag3
        "user_id": "test_user",
        "document_id": 999
    }
    
    with open("test/data_sources/sample_sk.txt", 'rb') as f:
        files2 = {'file': ('sample_sk.txt', f, 'text/plain')}
        data2 = {
            'collection_name': 'documents',
            'doc_meta': json.dumps(doc_meta_updated),
            'debug': 'true'
        }
        
        response2 = local_client.post("/api/v1/documents/embed", headers=headers, files=files2, data=data2)
    data2 = response2.json()
    doc_hash2 = data2["doc_hash"]
    applog.info(f"3. Created updated document with hash: {doc_hash2}")
    assert response2.status_code == 200
    assert doc_hash1 != doc_hash2  # Hash should be different due to tag change
    
    # 4. Delete the final document
    delete_final = {"collection_name": "documents", "doc_hash": doc_hash2}
    response3 = local_client.request("DELETE", "/api/v1/documents/delete", headers=headers, json=delete_final)
    data3 = response3.json()
    applog.info(f"4. Deleted final document: {data3}")
    assert response3.status_code == 200
    assert data3["deleted"] == True
    
    applog.info("Test completed - collection should be empty of test documents")


@pytest.mark.embedder
@pytest.mark.local
def test_duplicate_document_hash_detection(local_client, auth_token):
    """Test that same file name and tags produce same hash and skip processing"""
    import json

    headers = {"Authorization": f"Bearer {auth_token}"}
    
    # 1. Create document with specific tags
    doc_meta = {
        "tags": ["duplicate_test", "same_tags"],
        "user_id": "test_user",
        "document_id": 777,
        "file_name": "duplicate_test.txt"
    }
    
    with open("test/data_sources/sample_sk.txt", 'rb') as f:
        files = {'file': ('duplicate_test.txt', f, 'text/plain')}
        data = {
            'collection_name': 'documents',
            'doc_meta': json.dumps(doc_meta),
            'debug': 'true'
        }
        
        response1 = local_client.post("/api/v1/documents/embed", headers=headers, files=files, data=data)
    
    data1 = response1.json()
    doc_hash1 = data1["doc_hash"]
    applog.info(f"1. First embed - hash: {doc_hash1}")
    assert response1.status_code == 200
    
    # 2. Try to embed the SAME document again (same file name + same tags)
    with open("test/data_sources/sample_sk.txt", 'rb') as f:
        files2 = {'file': ('duplicate_test.txt', f, 'text/plain')}
        data2 = {
            'collection_name': 'documents',
            'doc_meta': json.dumps(doc_meta),
            'debug': 'true'
        }
        
        response2 = local_client.post("/api/v1/documents/embed", headers=headers, files=files2, data=data2)
    
    data2 = response2.json()
    doc_hash2 = data2["doc_hash"]
    applog.info(f"2. Duplicate embed - hash: {doc_hash2}")
    assert response2.status_code == 200
    assert doc_hash1 == doc_hash2
    
    # Cleanup
    delete_data = {"collection_name": "documents", "doc_hash": doc_hash1}
    local_client.request("DELETE", "/api/v1/documents/delete", headers=headers, json=delete_data)
    
    applog.info("Duplicate detection test completed successfully")



