import pytest
import json
from app.core.app_logger import get_logger

applog = get_logger(logger_name=__name__)

@pytest.mark.embedder
@pytest.mark.local
@pytest.mark.llm
def test_slovak_document_llm_questions(local_client, auth_token):
    """Test LLM questions about Slovak document - specific document and tag-based"""
    
    headers = {"Authorization": f"Bearer {auth_token}"}
    
    # 1. Embed Slovak document with "mountains" tag
    doc_meta = {
        "tags": ["mountains"],
        "user_id": "test_user",
        "document_id": 555,
        "file_name": "sample_sk.txt"
    }
    
    with open("test/data_sources/sample_sk.txt", 'rb') as f:
        files = {'file': ('sample_sk.txt', f, 'text/plain')}
        data = {
            'collection_name': 'documents',
            'doc_meta': json.dumps(doc_meta),
            'debug': 'true'
        }
        
        embed_response = local_client.post("/api/v1/documents/embed", headers=headers, files=files, data=data)
    
    embed_data = embed_response.json()
    doc_hash = embed_data["doc_hash"]
    applog.info(f"Embedded Slovak document with hash: {doc_hash}")
    assert embed_response.status_code == 200
    
    # 2. Ask question about the specific document
    document_question = {
        "question": "Kde je Slovensko?",
        "document_id": "555",
        "chat_history": []
    }
    
    doc_response = local_client.post("/api/v1/rag/ask", headers=headers, json=document_question)
    doc_answer = doc_response.json()
    
    print("\n=== QUESTION ABOUT DOCUMENT ===")
    print(f"Question: {document_question['question']}")
    print(f"Answer: {doc_answer.get('result', 'No answer')}")
    print("=" * 50)
    
    assert doc_response.status_code == 200
    
    # 3. Ask question about mountains using tags
    tag_question = {
        "question": "Aký je vysoky Gerlachovsky Stit?",
        "tags": ["mountains"],
        "chat_history": []
    }
    
    tag_response = local_client.post("/api/v1/rag/ask", headers=headers, json=tag_question)
    tag_answer = tag_response.json()
    
    print("\n=== QUESTION ABOUT MOUNTAINS TAG ===")
    print(f"Question: {tag_question['question']}")
    print(f"Answer: {tag_answer.get('result', 'No answer')}")
    print("=" * 50)
    
    assert tag_response.status_code == 200
    
    # 4. Delete the document
    delete_data = {
        "collection_name": "documents",
        "doc_hash": doc_hash
    }
    
    delete_response = local_client.request("DELETE", "/api/v1/documents/delete", headers=headers, json=delete_data)
    delete_result = delete_response.json()
    
    applog.info(f"Deleted Slovak document: {delete_result}")
    assert delete_response.status_code == 200
    assert delete_result["deleted"] == True
    
    applog.info("Slovak LLM test completed successfully")