import pytest
import requests
from pprint import pp

from app.core.app_logger import get_logger
from app.services.processing.ingestion import run_embedder

applog = get_logger(logger_name=__name__)


# @pytest.mark.skip()
@pytest.mark.embedder
@pytest.mark.local
@pytest.mark.parametrize(
    "file_path,tags",
    [
        ("test/data_sources/sample_sk.txt", ["important", "car"]),
        # ("test/data_sources/job.pdf", ["insurance", "car"]),
        # ("test/data_sources/ideas.docx", ["car"]),
    ],
)
def test_embedder(file_path, tags):

    collection_name = "documents"

    doc_meta = {}
    doc_meta["tags"] = tags

    run_embedder(
        collection_name=collection_name,
        file_path=file_path,
        doc_meta=doc_meta,
        debug=True,
    )
    assert True
