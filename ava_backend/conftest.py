import pytest
import requests
import os

from app.core.app_logger import get_logger

applog = get_logger(logger_name=__name__)


@pytest.fixture(scope="session")
def settings():
    from app.core.app_config import get_settings_from

    # Load settings
    if os.environ.get("RUNNING_ON_RENDER") == "True":
        env_file_location = "/etc/secrets/.env"
    else:
        env_file_location = os.path.join(os.path.dirname(__file__), ".env")

    applog.info(f"CONFTEST: Loading settings from {env_file_location}")
    return get_settings_from(env_file_path=env_file_location)


@pytest.fixture
def auth_token(settings):
    """Authenticate with Supabase and return access token"""
    auth_url = f"{settings.supabase_url}/auth/v1/token?grant_type=password"

    auth_data = {"email": "<EMAIL>", "password": "abc12345!"}

    headers = {"apikey": settings.supabase_anonkey, "Content-Type": "application/json"}

    response = requests.post(auth_url, json=auth_data, headers=headers)
    assert response.status_code == 200

    return response.json()["access_token"]
