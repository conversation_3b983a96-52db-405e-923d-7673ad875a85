import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const renderApiUrl = Deno.env.get('RENDER_API_URL') || 'https://ava-api-n0z0.onrender.com'

serve(async (req) => {
  try {
    const { document_id, user_id } = await req.json()
    
    if (!document_id || !user_id) {
      return new Response(JSON.stringify({ error: 'document_id and user_id required' }), { status: 400 })
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    const authHeader = req.headers.get('Authorization')

    // Get document with hash
    const { data: document, error: docError } = await supabase
      .from('documents')
      .select('id, qdrant_hash')
      .eq('id', document_id)
      .eq('user_id', user_id)
      .single()

    if (docError || !document) {
      return new Response(JSON.stringify({ error: 'Document not found' }), { status: 404 })
    }

    // Delete from Qdrant if hash exists
    if (document.qdrant_hash) {
      const serviceApiKey = Deno.env.get('RENDER_SUPABASE_FUNC_API_KEY')!
      const deleteResponse = await fetch(`${renderApiUrl}/api/v1/documents/delete_ss`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          collection_name: 'documents',
          doc_hash: document.qdrant_hash,
          service_key: serviceApiKey
        })
      })

      if (!deleteResponse.ok) {
        return new Response(JSON.stringify({ 
          error: 'Failed to delete from vector store',
          details: await deleteResponse.text()
        }), { status: 500 })
      }
    }

    // Delete document_tags
    await supabase
      .from('document_tags')
      .delete()
      .eq('document_id', document_id)

    // Delete document from Supabase
    const { error: deleteError } = await supabase
      .from('documents')
      .delete()
      .eq('id', document_id)
      .eq('user_id', user_id)

    if (deleteError) {
      return new Response(JSON.stringify({ error: deleteError.message }), { status: 500 })
    }

    return new Response(JSON.stringify({ 
      message: 'Document deleted successfully',
      document_id: document_id,
      deleted_from_vector_store: !!document.qdrant_hash
    }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200
    })

  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500
    })
  }
})