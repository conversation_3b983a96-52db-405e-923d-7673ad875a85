import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const renderApiUrl = Deno.env.get('RENDER_API_URL') || 'https://ava-api-n0z0.onrender.com'
const serviceApiKey = Deno.env.get('RENDER_SUPABASE_FUNC_API_KEY')!

serve(async (req) => {
  try {
    const { document_id, user_id } = await req.json()
    
    if (!document_id || !user_id) {
      return new Response(JSON.stringify({ error: 'document_id and user_id required' }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    console.log(`Starting sync for document ${document_id}`)
    
    // 1. Get document info from database
    const { data: document, error: docError } = await supabase
      .from('documents')
      .select(`
        id, storage_path, file_name, qdrant_hash,
        document_tags(
          embedding_status,
          tags(name)
        )
      `)
      .eq('id', document_id)
      .eq('user_id', user_id)
      .single()
    
    if (docError || !document) {
      console.log('Document not found:', docError?.message)
      return new Response(JSON.stringify({ error: 'Document not found' }), { 
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    
    console.log(`Found document: ${document.file_name}`)
    
    // 2. Get active tags (exclude deleted_locally)
    const activeTags = (document.document_tags || [])
      .filter(dt => dt.embedding_status !== 'deleted_locally')
      .map(dt => dt.tags.name)
    
    console.log(`Active tags: ${activeTags.join(', ')}`)
    
    // 3. Download file from Supabase Storage
    console.log(`Downloading file: ${document.storage_path}`)
    const { data: fileBlob, error: storageError } = await supabase.storage
      .from('documents')
      .download(document.storage_path)
    
    if (storageError || !fileBlob) {
      console.log('Storage error:', storageError?.message)
      return new Response(JSON.stringify({ error: 'Failed to download file' }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    
    console.log(`File downloaded, size: ${fileBlob.size} bytes`)
    
    // 4. Create form data for Render API
    const formData = new FormData()
    formData.append('file', fileBlob, document.file_name)
    formData.append('collection_name', 'documents')
    formData.append('doc_meta', JSON.stringify({
      user_id: user_id,
      document_id: document.id,
      file_name: document.file_name,
      tags: activeTags,
      qdrant_hash: document.qdrant_hash
    }))
    formData.append('debug', 'true')
    formData.append('service_key', serviceApiKey)
    
    // 5. Call Render API shared secret endpoint
    console.log(`Calling Render API: ${renderApiUrl}/api/v1/documents/embed_ss`)
    const embedResponse = await fetch(`${renderApiUrl}/api/v1/documents/embed_ss`, {
      method: 'POST',
      body: formData
    })
    
    const responseText = await embedResponse.text()
    console.log(`Render response: ${embedResponse.status} - ${responseText}`)
    
    // 6. Update database if successful
    if (embedResponse.ok) {
      try {
        const embedData = JSON.parse(responseText)
        
        // Update document with hash and status
        await supabase
          .from('documents')
          .update({
            qdrant_hash: embedData.doc_hash,
            embedding_status: 'completed'
          })
          .eq('id', document_id)
        
        // Update tag statuses
        await supabase
          .from('document_tags')
          .update({ embedding_status: 'embedded' })
          .eq('document_id', document_id)
          .in('embedding_status', ['new', 'changed'])
        
        // Delete locally deleted tags
        await supabase
          .from('document_tags')
          .delete()
          .eq('document_id', document_id)
          .eq('embedding_status', 'deleted_locally')
        
        console.log('Database updated successfully')
      } catch (parseError) {
        console.log('Failed to parse response or update database:', parseError.message)
      }
    }
    
    // Return Render API response
    return new Response(responseText, {
      status: embedResponse.status,
      headers: { 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.log('Function error:', error.message)
    console.log('Stack:', error.stack)
    
    return new Response(JSON.stringify({ 
      error: error.message,
      type: error.constructor.name
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})