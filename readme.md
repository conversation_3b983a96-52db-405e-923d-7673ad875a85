
# FastAPI Backend
## Local setup

To run locally:
uv run uvicorn main:fapp --reload

To check locally:
http://127.0.0.1:8000/health

## Render Deployment

### Build
```
cd ava_backend && pip install uv && uv sync --active

```

### Start
```
cd ava_backend && uvicorn main:fapp --host 0.0.0.0 --port $PORT

```


# Weaviate 
## Setup on fly.io

1. Install cli:
```
curl -L https://fly.io/install.sh | sh
flyctl was installed successfully to /home/<USER>/.fly/bin/flyctl
Manually add the directory to your $HOME/.bash_profile (or similar)
  export FLYCTL_INSTALL="/home/<USER>/.fly"
  export PATH="$FLYCTL_INSTALL/bin:$PATH"
Run '/home/<USER>/.fly/bin/flyctl --help' to get started

```

(I have access via Github)
1. Create 2 volumes in FRA 1st
fly volume create weaviate_data -r fra -n 1

2. 
fly deploy

3. 
To suspend:
fly scale count 0 -a ava-weaviate

# To redeploy
fly apps destroy ava-weaviate
fly launch --no-deploy
fly volume create weaviate_data -r fra -n 1
fly deploy
fly scale count 1


# Troubleshoot

curl -v https://ava-weaviate.fly.dev/caddy-health
curl -v http://localhost/caddy-health
fly ssh console -a ava-weaviate

ps aux | grep caddy
netstat -tlnp | grep :80

## New pass for caddy proxy
htpasswd -nB avauser