-- Create Supabase Edge Function for embedding sync
CREATE OR REPLACE FUNCTION sync_documents_to_embedding(user_id_param UUID)
RETURNS JSON AS $$
DECLARE
    doc RECORD;
    tags_array TEXT[];
    embed_response JSON;
    result JSON;
BEGIN
    result := '{"processed": [], "errors": []}'::JSON;
    
    -- Get documents that need embedding
    FOR doc IN 
        SELECT d.*, 
               ARRAY_AGG(t.name) FILTER (WHERE t.name IS NOT NULL) as tag_names
        FROM documents d
        LEFT JOIN document_tags dt ON d.id = dt.document_id
        LEFT JOIN tags t ON dt.tag_id = t.id
        WHERE d.user_id = user_id_param 
        AND d.embedding_status = 'pending'
        GROUP BY d.id
    LOOP
        BEGIN
            -- Call external embedding API
            SELECT net.http_post(
                url := 'https://ava-api-n0z0.onrender.com/api/v1/documents/embed',
                headers := jsonb_build_object(
                    'Content-Type', 'application/json',
                    'Authorization', 'Bearer ' || auth.jwt()
                ),
                body := jsonb_build_object(
                    'collection_name', 'documents',
                    'file_path', doc.storage_path,
                    'doc_meta', jsonb_build_object(
                        'user_id', user_id_param,
                        'document_id', doc.id,
                        'title', doc.file_name,
                        'tags', COALESCE(doc.tag_names, ARRAY[]::TEXT[])
                    )
                )
            ) INTO embed_response;
            
            -- Update document if successful
            IF (embed_response->>'status_code')::INT = 200 THEN
                UPDATE documents 
                SET embedding_status = 'completed',
                    qdrant_point_id = (embed_response->'body'->>'doc_hash')::UUID
                WHERE id = doc.id;
                
                -- Update document_tags
                UPDATE document_tags 
                SET embedding_status = 'embedded'
                WHERE document_id = doc.id;
                
                result := jsonb_set(
                    result::jsonb, 
                    '{processed}', 
                    (result::jsonb->'processed') || jsonb_build_array(doc.id)
                );
            ELSE
                result := jsonb_set(
                    result::jsonb, 
                    '{errors}', 
                    (result::jsonb->'errors') || jsonb_build_array(
                        jsonb_build_object('document_id', doc.id, 'error', embed_response)
                    )
                );
            END IF;
            
        EXCEPTION WHEN OTHERS THEN
            result := jsonb_set(
                result::jsonb, 
                '{errors}', 
                (result::jsonb->'errors') || jsonb_build_array(
                    jsonb_build_object('document_id', doc.id, 'error', SQLERRM)
                )
            );
        END;
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;