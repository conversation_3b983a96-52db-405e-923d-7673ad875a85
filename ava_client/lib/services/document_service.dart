import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';

class DocumentService {
  static final _supabase = Supabase.instance.client;
  static const String _apiBaseUrl = 'https://ava-api-n0z0.onrender.com';

  static Future<List<Map<String, dynamic>>> loadDocuments() async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    final response = await _supabase
        .from('documents')
        .select('*, document_tags(tags(id, name, color), embedding_status)')
        .eq('user_id', userId);

    return List<Map<String, dynamic>>.from(response);
  }

  static Future<Map<String, dynamic>> loadDocument(int documentId) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    return await _supabase
        .from('documents')
        .select('*, document_tags(tags(id, name, color), embedding_status)')
        .eq('user_id', userId)
        .eq('id', documentId)
        .single();
  }

  static Future<void> uploadDocument(File file, String fileName) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    final userFolderPath = 'private/$userId';
    
    await _supabase.storage
        .from('documents')
        .upload(
          '$userFolderPath/$fileName',
          file,
          fileOptions: const FileOptions(upsert: true),
        );

    await _supabase.from('documents').upsert({
      'user_id': userId,
      'file_name': fileName,
      'storage_path': '$userFolderPath/$fileName',
      'embedding_status': 'pending', // Set to pending initially
    });
  }

  static Future<void> deleteDocument(String fileName) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    final userFolderPath = 'private/$userId';
    
    await _supabase.storage.from('documents').remove([
      '$userFolderPath/$fileName',
    ]);

    await _supabase
        .from('documents')
        .delete()
        .eq('user_id', userId)
        .eq('file_name', fileName);
  }

  static Future<void> markAsEmbedded(String fileName) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    await _supabase
        .from('documents')
        .update({'embedding_status': 'completed'}) // Set to completed
        .eq('user_id', userId)
        .eq('file_name', fileName);
  }

  static Future<void> processDocument(int documentId) async {
    await _supabase
        .from('documents')
        .update({'embedding_status': 'completed'}) // Set to completed
        .eq('id', documentId);

    // Mark all non-deleted document tags as embedded and remove deleted ones
    await _supabase
        .from('document_tags')
        .update({'embedding_status': 'embedded'})
        .eq('document_id', documentId)
        .or('embedding_status.eq.new,embedding_status.eq.changed');
    
    await _supabase
        .from('document_tags')
        .delete()
        .eq('document_id', documentId)
        .eq('embedding_status', 'deleted_locally');
  }

  static Future<void> processDocumentByFileName(String fileName, List<Map<String, dynamic>> documents) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    // Get document ID first
    final doc = documents.firstWhere(
      (d) => d['file_name'] == fileName,
      orElse: () => <String, dynamic>{},
    );
    if (doc.isEmpty) throw Exception('Document not found');

    await _supabase
        .from('documents')
        .update({'embedding_status': 'completed'}) // Set to completed
        .eq('user_id', userId)
        .eq('file_name', fileName);

    // Mark all non-deleted document tags as embedded and remove deleted ones
    await _supabase
        .from('document_tags')
        .update({'embedding_status': 'embedded'})
        .eq('document_id', doc['id'])
        .or('embedding_status.eq.new,embedding_status.eq.changed');
    
    await _supabase
        .from('document_tags')
        .delete()
        .eq('document_id', doc['id'])
        .eq('embedding_status', 'deleted_locally');
  }

  static Future<void> processMultipleDocuments(List<String> fileNames, List<Map<String, dynamic>> documents) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    for (String fileName in fileNames) {
      await _supabase
          .from('documents')
          .update({'embedding_status': 'completed'}) // Set to completed
          .eq('user_id', userId)
          .eq('file_name', fileName);

      // Get document for tag processing
      final doc = documents.firstWhere(
        (d) => d['file_name'] == fileName,
        orElse: () => <String, dynamic>{},
      );
      if (doc.isEmpty) continue;
      
      await _supabase
          .from('document_tags')
          .update({'embedding_status': 'embedded'})
          .eq('document_id', doc['id'])
          .or('embedding_status.eq.new,embedding_status.eq.changed');

      await _supabase
          .from('document_tags')
          .delete()
          .eq('document_id', doc['id'])
          .eq('embedding_status', 'deleted_locally');
    }
  }

  static Future<void> syncDocumentToEmbedding(int documentId) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      print("------> Syncing document $documentId for user: $userId (using shared secret)");
      
      final response = await _supabase.functions.invoke('sync-embed-ss', body: {
        'document_id': documentId,
        'user_id': userId
      });

      print("------> Response status: ${response.status}, data: ${response.data}");

      if (response.status != 200) {
        throw Exception('Sync failed: ${response.data}');
      }

      print('------> Sync completed successfully');
    } catch (e) {
      print('------> Error in syncDocumentToEmbedding: $e');
      rethrow;
    }
  }

  static Future<void> deleteDocumentById(int documentId) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    // Call the Supabase function
    print("------> Calling 'sync-delete-ss'");
    final response = await _supabase.functions.invoke('sync-delete-ss', body: {
      'document_id': documentId,
      'user_id': userId
    });

    if (response.status != 200) {
      throw Exception('Delete failed: ${response.data}');
    }

    print('Delete completed: ${response.data}');
  }
}