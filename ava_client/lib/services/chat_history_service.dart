import 'package:supabase_flutter/supabase_flutter.dart';

class ChatSession {
  final String id;
  final String userId;
  final String? sessionName;
  final String? documentId;
  final List<String>? tags;
  final DateTime createdAt;
  final DateTime updatedAt;

  ChatSession({
    required this.id,
    required this.userId,
    this.sessionName,
    this.documentId,
    this.tags,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      id: json['id'],
      userId: json['user_id'],
      sessionName: json['session_name'],
      documentId: json['document_id'],
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'session_name': sessionName,
      'document_id': documentId,
      'tags': tags,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class ChatMessage {
  final String id;
  final String sessionId;
  final String userId;
  final String role; // 'user' or 'assistant'
  final String content;
  final DateTime createdAt;

  ChatMessage({
    required this.id,
    required this.sessionId,
    required this.userId,
    required this.role,
    required this.content,
    required this.createdAt,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      sessionId: json['session_id'],
      userId: json['user_id'],
      role: json['role'],
      content: json['content'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'session_id': sessionId,
      'user_id': userId,
      'role': role,
      'content': content,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Convert to format expected by RAG service
  Map<String, dynamic> toRagFormat() {
    return {
      'role': role,
      'content': content,
    };
  }
}

class ChatHistoryService {
  static final _supabase = Supabase.instance.client;

  /// Create a new chat session
  static Future<ChatSession> createChatSession({
    String? documentId,
    List<String>? tags,
    String? sessionName,
  }) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    final response = await _supabase
        .from('chat_sessions')
        .insert({
          'user_id': userId,
          'session_name': sessionName,
          'document_id': documentId,
          'tags': tags,
        })
        .select()
        .single();

    return ChatSession.fromJson(response);
  }

  /// Get all chat sessions for the current user, ordered by most recent first
  static Future<List<ChatSession>> getChatSessions() async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    final response = await _supabase
        .from('chat_sessions')
        .select()
        .eq('user_id', userId)
        .order('updated_at', ascending: false);

    return response.map<ChatSession>((json) => ChatSession.fromJson(json)).toList();
  }

  /// Get a specific chat session
  static Future<ChatSession?> getChatSession(String sessionId) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    final response = await _supabase
        .from('chat_sessions')
        .select()
        .eq('id', sessionId)
        .eq('user_id', userId)
        .maybeSingle();

    return response != null ? ChatSession.fromJson(response) : null;
  }

  /// Delete a chat session and all its messages
  static Future<void> deleteChatSession(String sessionId) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    // Delete messages first (due to foreign key constraint)
    await _supabase
        .from('chat_messages')
        .delete()
        .eq('session_id', sessionId)
        .eq('user_id', userId);

    // Delete the session
    await _supabase
        .from('chat_sessions')
        .delete()
        .eq('id', sessionId)
        .eq('user_id', userId);
  }

  /// Add a message to a chat session
  static Future<ChatMessage> addMessage({
    required String sessionId,
    required String role,
    required String content,
  }) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    final response = await _supabase
        .from('chat_messages')
        .insert({
          'session_id': sessionId,
          'user_id': userId,
          'role': role,
          'content': content,
        })
        .select()
        .single();

    // Update session's updated_at timestamp
    await _supabase
        .from('chat_sessions')
        .update({'updated_at': DateTime.now().toIso8601String()})
        .eq('id', sessionId)
        .eq('user_id', userId);

    return ChatMessage.fromJson(response);
  }

  /// Get all messages for a chat session
  static Future<List<ChatMessage>> getChatMessages(String sessionId) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    final response = await _supabase
        .from('chat_messages')
        .select()
        .eq('session_id', sessionId)
        .eq('user_id', userId)
        .order('created_at', ascending: true);

    return response.map<ChatMessage>((json) => ChatMessage.fromJson(json)).toList();
  }

  /// Update session name
  static Future<void> updateSessionName(String sessionId, String sessionName) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    await _supabase
        .from('chat_sessions')
        .update({
          'session_name': sessionName,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('id', sessionId)
        .eq('user_id', userId);
  }
}
