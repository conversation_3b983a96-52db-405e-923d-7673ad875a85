import 'package:flutter/foundation.dart';
import 'voice_service.dart';
import 'service_locator.dart';

class LanguageService extends ChangeNotifier {
  SupportedLanguage _currentLanguage = SupportedLanguage.english;

  SupportedLanguage get currentLanguage => _currentLanguage;

  Future<void> setLanguage(SupportedLanguage language) async {
    print('LanguageService: Setting language to ${language.code}');
    _currentLanguage = language;
    await getIt<VoiceService>().setLanguage(language);
    print('LanguageService: Voice service updated');
    notifyListeners();
  }
}