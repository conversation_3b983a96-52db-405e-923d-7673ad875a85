import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';

enum SupportedLanguage {
  english('en-US', 'English'),
  slovak('sk-SK', 'Slovenčina'),
  hungarian('hu-HU', 'Magyar');

  const SupportedLanguage(this.code, this.displayName);
  final String code;
  final String displayName;
}

class VoiceService {
  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  
  SupportedLanguage _currentLanguage = SupportedLanguage.english;
  bool _isInitialized = false;

  SupportedLanguage get currentLanguage => _currentLanguage;
  bool get isInitialized => _isInitialized;

  Future<bool> initialize() async {
    try {
      final speechAvailable = await _speechToText.initialize();
      await _flutterTts.setLanguage(_currentLanguage.code);
      _isInitialized = speechAvailable;
      return _isInitialized;
    } catch (e) {
      _isInitialized = false;
      return false;
    }
  }

  Future<void> setLanguage(SupportedLanguage language) async {
    _currentLanguage = language;
    if (_isInitialized) {
      await _flutterTts.setLanguage(language.code);
    }
  }

  Future<void> startListening({
    required Function(String) onResult,
    Function(String)? onError,
  }) async {
    if (!_isInitialized) return;

    await _speechToText.listen(
      onResult: (result) => onResult(result.recognizedWords),
      localeId: _currentLanguage.code,
      onSoundLevelChange: null,
    );
  }

  Future<void> stopListening() async {
    await _speechToText.stop();
  }

  bool get isListening => _speechToText.isListening;

  Future<void> speak(String text) async {
    if (!_isInitialized) return;
    await _flutterTts.speak(text);
  }

  Future<void> stop() async {
    await _flutterTts.stop();
  }

  void dispose() {
    _speechToText.cancel();
    _flutterTts.stop();
  }
}