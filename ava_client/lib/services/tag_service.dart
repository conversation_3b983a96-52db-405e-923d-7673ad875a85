import 'package:supabase_flutter/supabase_flutter.dart';

class TagService {
  static final _supabase = Supabase.instance.client;

  static Future<List<Map<String, dynamic>>> loadTags() async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) throw Exception('User not authenticated');

    final response = await _supabase
        .from('tags')
        .select()
        .eq('user_id', userId);

    return List<Map<String, dynamic>>.from(response);
  }

  static Future<void> assignTagsToDocument(int documentId, List<int> tagIds) async {
    // Get existing tag IDs for this document
    final existingTags = await _supabase
        .from('document_tags')
        .select('tag_id, embedding_status')
        .eq('document_id', documentId);

    final existingTagIds = existingTags
        .map((dt) => dt['tag_id'] as int)
        .toSet();

    // Only insert new tags
    for (int tagId in tagIds) {
      if (!existingTagIds.contains(tagId)) {
        await _supabase.from('document_tags').upsert({
          'document_id': documentId,
          'tag_id': tagId,
          'embedding_status': 'new',
        });
      }
    }

    // Mark unchecked tags as deleted
    for (int existingTagId in existingTagIds) {
      if (!tagIds.contains(existingTagId)) {
        await _supabase.from('document_tags').update({
          'embedding_status': 'deleted_locally',
        }).match({
          'document_id': documentId,
          'tag_id': existingTagId,
        });
      }
    }
    
    // Restore previously deleted tags that are now selected
    for (int tagId in tagIds) {
      if (existingTagIds.contains(tagId)) {
        await _supabase.from('document_tags').update({
          'embedding_status': 'new',
        }).match({
          'document_id': documentId,
          'tag_id': tagId,
        });
      }
    }
  }

  static Future<void> assignTagsToFiles(List<String> fileNames, List<int> tagIds, List<Map<String, dynamic>> documents) async {
    for (String fileName in fileNames) {
      final doc = documents.firstWhere(
        (d) => d['file_name'] == fileName,
        orElse: () => <String, dynamic>{},
      );
      if (doc.isEmpty) continue;

      final documentId = doc['id'];
      final docTags = doc['document_tags'] as List<dynamic>? ?? [];
      final existingTagIds = docTags
          .map((dt) => dt['tags']?['id'])
          .where((id) => id != null)
          .cast<int>()
          .toSet();

      // Only insert new tags
      for (int tagId in tagIds) {
        if (!existingTagIds.contains(tagId)) {
          await _supabase.from('document_tags').insert({
            'document_id': documentId,
            'tag_id': tagId,
            'embedding_status': 'new',
          });
        }
      }

      // Remove unchecked tags
      for (int existingTagId in existingTagIds) {
        if (!tagIds.contains(existingTagId)) {
          await _supabase
              .from('document_tags')
              .delete()
              .eq('document_id', documentId)
              .eq('tag_id', existingTagId);
        }
      }
    }
  }
}