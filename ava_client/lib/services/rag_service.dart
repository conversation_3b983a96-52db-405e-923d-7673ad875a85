import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';

class RagService {
  static final _supabase = Supabase.instance.client;
  static const String _apiBaseUrl = 'https://ava-api-n0z0.onrender.com';

  static Future<String> askQuestion({
    required String question,
    String? documentId,
    List<String>? tags,
    List<Map<String, dynamic>>? chatHistory,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      final token = _supabase.auth.currentSession?.accessToken;
      if (token == null) throw Exception('No access token');

      print('Making request to: $_apiBaseUrl/api/v1/rag/ask');
      print('Document ID: $documentId, Tags: $tags');

      final response = await http.post(
        Uri.parse('$_apiBaseUrl/api/v1/rag/ask'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'question': question,
          'document_id': documentId,
          'tags': tags,
          'chat_history': chatHistory ?? [],
        }),
      ).timeout(const Duration(seconds: 30));

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['result'] ?? 'No answer received';
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('Error in askQuestion: $e');
      rethrow;
    }
  }
}