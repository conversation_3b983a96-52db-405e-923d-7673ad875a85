import 'package:flutter_localization/flutter_localization.dart';

const List<MapLocale> LOCALES = [
  MapLocale("en", LocaleData.EN),
  MapLocale("hu", LocaleData.HU),
  MapLocale("sk", LocaleData.SK),
];

mixin LocaleData {
  static const String email = 'email';
  static const String password = 'password';
  static const String signup = 'sinup';
  static const String signup_message = 'signup_message';
  static const String login = 'login';
  static const String login_message = 'login_message';
  static const String title = 'title';
  static const String body = 'body';
  static const String error_loading_tags = 'error_loading_tags';
  static const String error_creating_tag = 'error_creating_tag';
  static const String error_updating_tag = 'error_updating_tag';
  static const String error_deleting_tag = 'error_deleting_tag';
  static const String pick_color = 'pick_color';
  static const String create = 'create';
  static const String update = 'update';
  static const String tag_name = 'tag_name';
  static const String color = 'color';
  static const String cancel = 'cancel';
  static const String create_tag = 'create_tag';
  static const String edit_tag = 'edit_tag';
  static const String tags = 'tags';
  static const String no_tags_found = 'no_tags_found';
  static const String no_name = 'no_name';
  static const String no_documents_found = 'no_documents_found';
  static const String uploaded = 'uploaded';
  static const String invalid_filename = 'invalid_filename';
  static const String error_uploading_file = 'error_uploading_file';
  static const String error_deleting_file = 'error_deleting_file';
  static const String unknown = 'unknown';
  static const String delete_document = 'delete_document';
  static const String delete_document_confirm = 'delete_document_confirm';
  static const String delete = 'delete';
  static const String sending_to_llm = 'sending_to_llm';
  static const String select_all = 'select_all';
  static const String deselect_all = 'deselect_all';
  static const String assign_tag = 'assign_tag';
  static const String select_tag = 'select_tag';
  static const String tag_assigned = 'tag_assigned';
  static const String selected = 'selected';
  static const String file_already_exists = 'file_already_exists';
  static const String document_details = 'document_details';
  static const String embedding_status = 'embedding_status';
  static const String no_tags = 'no_tags';
  static const String send = 'send';
  static const String talk = 'talk';
  static const String read = 'read';
  static const String processed = 'processed';
  static const String not_processed = 'not_processed';
  static const String embedding_document = 'embedding_document';
  static const String already_processed = 'already_processed';
  static const String reprocess_warning = 'reprocess_warning';
  static const String proceed = 'proceed';
  static const String processed_tags_changed = 'processed_tags_changed';
  static const String select_language = 'select_language';
  static const String logout_error = 'logout_error';
  static const String error = 'error';
  static const String ok = 'ok';
  static const String delete_documents = 'delete_documents';
  static const String delete_documents_confirm = 'delete_documents_confirm';
  static const String documents = 'documents';
  static const String sort_by_name = 'sort_by_name';
  static const String sort_by_date = 'sort_by_date';
  static const String with_tags = 'with_tags';
  static const String delete_tag = 'delete_tag';
  static const String delete_tag_confirm = 'delete_tag_confirm';
  static const String chat_error = 'chat_error';
  static const String document_must_be_processed = 'document_must_be_processed';
  static const String click_process_button = 'click_process_button';
  static const String unknown_tag = 'unknown_tag';
  static const String documents_with_tag = 'documents_with_tag';
  static const String chat_about_tag = 'chat_about_tag';
  static const String ask_about_tag = 'ask_about_tag';
  static const String chat_about_document = 'chat_about_document';
  static const String ask_about_document = 'ask_about_document';
  static const String chat_about_documents = 'chat_about_documents';
  static const String ask_about_documents = 'ask_about_documents';
  static const String documents_with_tags = 'documents_with_tags';
  static const String ask_about_documents_with_tags = 'ask_about_documents_with_tags';

  static const Map<String, dynamic> EN = {
    login: 'Log in',
    login_message: 'Have an account? Please log in.',
    signup: 'Sign up',
    signup_message: 'Need an account? Sign up.',
    email: 'E-mail',
    password: 'Password',
    title: 'AVA',
    body: 'Welcome to Flutter Localized App',
    error_loading_tags: 'Error loading tags',
    error_creating_tag: 'Error creating tag',
    error_updating_tag: 'Error updating tag',
    error_deleting_tag: 'Error deleting tag',
    pick_color: 'Pick a color',
    create: 'Create',
    update: 'Update',
    tag_name: 'Tag name',
    color: 'Color',
    cancel: 'Cancel',
    create_tag: 'Create Tag',
    edit_tag: 'Edit Tag',
    tags: 'Tags',
    no_tags_found: 'No tags found',
    no_name: 'No name',
    no_documents_found: 'No documents found',
    uploaded: 'Uploaded',
    invalid_filename:
        'Invalid filename. Please use English letters, numbers, underscores and no spaces.',
    error_uploading_file: 'Error uploading file',
    error_deleting_file: 'Error deleting file',
    unknown: 'Unknown',
    delete_document: 'Delete Document',
    delete_document_confirm: 'Are you sure you want to delete this document?',
    delete: 'Delete',
    sending_to_llm: 'SENDING TO LLM',
    select_all: 'Select All',
    deselect_all: 'Deselect All',
    assign_tag: 'Assign Tag',
    select_tag: 'Select a tag',
    tag_assigned: 'Tag assigned successfully',
    selected: 'selected',
    file_already_exists: 'File already exists',
    document_details: 'Document Details',
    embedding_status: 'Processing status',
    no_tags: 'No tags',
    send: 'Send',
    talk: 'Talk',
    read: 'Read',
    processed: 'Processed',
    not_processed: 'Not Processed',
    embedding_document: 'Processing document...',
    already_processed: 'Already Processed',
    reprocess_warning: 'This document is already processed. Reprocessing may involve extra costs and time. Continue?',
    proceed: 'Proceed',
    processed_tags_changed: 'Processed, local tag changed',
    select_language: 'Select Language',
    logout_error: 'Logout error',
    error: 'Error',
    ok: 'OK',
    delete_documents: 'Delete Documents',
    delete_documents_confirm: 'Are you sure you want to delete {count} document(s)?',
    documents: 'Documents',
    sort_by_name: 'Sort by Name',
    sort_by_date: 'Sort by Date',
    with_tags: 'With tags',
    delete_tag: 'Delete Tag',
    delete_tag_confirm: 'This tag is linked to {count} document(s). Are you sure you want to delete it?',
    chat_error: 'Error',
    document_must_be_processed: 'Document must be processed before chatting',
    click_process_button: 'Click the process button above to enable chat',
    unknown_tag: 'Unknown Tag',
    documents_with_tag: 'Documents with this tag ({count})',
    chat_about_tag: 'Chat about documents tagged with "{tag}"...',
    ask_about_tag: 'Ask about documents with "{tag}" tag...',
    chat_about_document: 'Chat about {document}...',
    ask_about_document: 'Ask about {document}...',
    chat_about_documents: 'Chat about documents...',
    ask_about_documents: 'Ask about documents...',
    documents_with_tags: 'documents with tags: {tags}',
    ask_about_documents_with_tags: 'Ask about documents with these tags...',
  };

  static const Map<String, dynamic> HU = {
    login: 'Bejelentkezés',
    login_message: 'Már van fiókja? Kérjük jelentkezzen be.',
    signup: 'Létrehozás',
    signup_message: 'Nincs még fiókja? Létrehozás.',
    email: 'E-mail',
    password: 'Jelszó',
    title: 'AVA',
    body: 'Udvozoljuk',
    error_loading_tags: 'Hiba a címkék betöltésekor',
    error_creating_tag: 'Hiba a címke létrehozásakor',
    error_updating_tag: 'Hiba a címke frissítésekor',
    error_deleting_tag: 'Hiba a címke törlésekor',
    pick_color: 'Válassz színt',
    create: 'Létrehozás',
    update: 'Frissítés',
    tag_name: 'Címke neve',
    color: 'Szín',
    cancel: 'Mégse',
    create_tag: 'Címke létrehozása',
    edit_tag: 'Címke szerkesztése',
    tags: 'Címkék',
    no_tags_found: 'Nincsenek címkék',
    no_name: 'Nincs név',
    no_documents_found: 'Nincsenek dokumentumok',
    uploaded: 'Feltöltve',
    invalid_filename:
        'Érvénytelen fájlnév. Kérjük használjon angol betűket, számokat, aláhúzást és ne használjon szóközöket.',
    error_uploading_file: 'Hiba a fájl feltöltésekor',
    error_deleting_file: 'Hiba a fájl törlésekor',
    unknown: 'Ismeretlen',
    delete_document: 'Dokumentum törlése',
    delete_document_confirm: 'Biztosan törölni szeretné ezt a dokumentumot?',
    delete: 'Törlés',
    sending_to_llm: 'KÜLDÉS LLM-NEK',
    select_all: 'Mindet kijelöl',
    deselect_all: 'Kijelölés megszüntetése',
    assign_tag: 'Címke hozzárendelése',
    select_tag: 'Válassz címkét',
    tag_assigned: 'Címke sikeresen hozzárendelve',
    selected: 'kiválasztva',
    file_already_exists: 'A fájl már létezik',
    document_details: 'Dokumentum részletei',
    embedding_status: 'Feldolgozási állapot',
    no_tags: 'Nincsenek címkék',
    send: 'Küldés',
    talk: 'Beszélgetés',
    read: 'Olvasás',
    processed: 'Feldolgozva',
    not_processed: 'Nincs feldolgozva',
    embedding_document: 'Dokumentum feldolgozása...',
    already_processed: 'Már feldolgozva',
    reprocess_warning: 'Ez a dokumentum már fel van dolgozva. Az újrafeldolgozás extra költségekkel és idővel járhat. Folytatja?',
    proceed: 'Folytatás',
    processed_tags_changed: 'Feldolgozva, helyi címke változott',
    select_language: 'Nyelv kiválasztása',
    logout_error: 'Kijelentkezési hiba',
    error: 'Hiba',
    ok: 'OK',
    delete_documents: 'Dokumentumok törlése',
    delete_documents_confirm: 'Biztosan törölni szeretné a(z) {count} dokumentumot?',
    documents: 'Dokumentumok',
    sort_by_name: 'Rendezés név szerint',
    sort_by_date: 'Rendezés dátum szerint',
    with_tags: 'Címkékkel',
    delete_tag: 'Címke törlése',
    delete_tag_confirm: 'Ez a címke {count} dokumentumhoz van kapcsolva. Biztosan törölni szeretné?',
    chat_error: 'Hiba',
    document_must_be_processed: 'A dokumentumot fel kell dolgozni a beszélgetés előtt',
    click_process_button: 'Kattintson a feldolgozás gombra a chat engedélyezéséhez',
    unknown_tag: 'Ismeretlen címke',
    documents_with_tag: 'Dokumentumok ezzel a címkével ({count})',
    chat_about_tag: 'Beszélgetés a "{tag}" címkével ellátott dokumentumokról...',
    ask_about_tag: 'Kérdezzen a "{tag}" címkével ellátott dokumentumokról...',
    chat_about_document: 'Beszélgetés a(z) {document} dokumentumról...',
    ask_about_document: 'Kérdezzen a(z) {document} dokumentumról...',
    chat_about_documents: 'Beszélgetés a dokumentumokról...',
    ask_about_documents: 'Kérdezzen a dokumentumokról...',
    documents_with_tags: 'dokumentumok címkékkel: {tags}',
    ask_about_documents_with_tags: 'Kérdezzen a címkézett dokumentumokról...',
  };

  static const Map<String, dynamic> SK = {
    login: 'Prihlásenie',
    login_message: 'Máte účet? Prosím prihláste sa.',
    signup: 'Registrácia',
    signup_message: 'Nemáte účet? Vytvoriť.',
    email: 'E-mail',
    password: 'Heslo',
    title: 'AVA',
    body: 'Vytajte v tejto appke',
    error_loading_tags: 'Chyba pri načítaní štítkov',
    error_creating_tag: 'Chyba pri vytváraní štítka',
    error_updating_tag: 'Chyba pri aktualizácii štítka',
    error_deleting_tag: 'Chyba pri mazaní štítka',
    pick_color: 'Vyberte farbu',
    create: 'Vytvoriť',
    update: 'Aktualizovať',
    tag_name: 'Názov štítka',
    color: 'Farba',
    cancel: 'Zrušiť',
    create_tag: 'Vytvoriť štítok',
    edit_tag: 'Upraviť štítok',
    tags: 'Štítky',
    no_tags_found: 'Žiadne štítky',
    no_name: 'Žiadny názov',
    no_documents_found: 'Žiadne dokumenty',
    uploaded: 'Nahraté',
    invalid_filename:
        'Neplatný názov súboru. Prosím použite anglické písmená, čísla, podčiarkovníky a nepoužívajte medzery.',
    error_uploading_file: 'Chyba pri nahrávaní súboru',
    error_deleting_file: 'Chyba pri mazaní súboru',
    unknown: 'Neznáme',
    delete_document: 'Zmazať dokument',
    delete_document_confirm: 'Ste si istí, že chcete zmazať tento dokument?',
    delete: 'Zmazať',
    sending_to_llm: 'ODOSIELANIE DO LLM',
    select_all: 'Vybrať všetko',
    deselect_all: 'Zrušiť výber',
    assign_tag: 'Priradiť štítok',
    select_tag: 'Vyberte štítok',
    tag_assigned: 'Štítok úspešne priradený',
    selected: 'vybrané',
    file_already_exists: 'Súbor už existuje',
    document_details: 'Detaily dokumentu',
    embedding_status: 'Stav spracovania',
    no_tags: 'Žiadne štítky',
    send: 'Odoslať',
    talk: 'Rozhovor',
    read: 'Čítať',
    processed: 'Spracované',
    not_processed: 'Nespracované',
    embedding_document: 'Spracovanie dokumentu...',
    already_processed: 'Už spracované',
    reprocess_warning: 'Tento dokument je už spracovaný. Opätovné spracovanie môže zahrnovať extra náklady a čas. Pokračovať?',
    proceed: 'Pokračovať',
    processed_tags_changed: 'Spracované, lokálny štítok zmenený',
    select_language: 'Vybrať jazyk',
    logout_error: 'Chyba pri odhlásení',
    error: 'Chyba',
    ok: 'OK',
    delete_documents: 'Zmazať dokumenty',
    delete_documents_confirm: 'Ste si istí, že chcete zmazať {count} dokumentov?',
    documents: 'Dokumenty',
    sort_by_name: 'Zoradiť podľa názvu',
    sort_by_date: 'Zoradiť podľa dátumu',
    with_tags: 'So štítkami',
    delete_tag: 'Zmazať štítok',
    delete_tag_confirm: 'Tento štítok je prepojený s {count} dokumentmi. Ste si istí, že ho chcete zmazať?',
    chat_error: 'Chyba',
    document_must_be_processed: 'Dokument musí byť spracovaný pred chatovaním',
    click_process_button: 'Kliknite na tlačidlo spracovania pre povolenie chatu',
    unknown_tag: 'Neznámy štítok',
    documents_with_tag: 'Dokumenty s týmto štítkom ({count})',
    chat_about_tag: 'Chatovať o dokumentoch označených "{tag}"...',
    ask_about_tag: 'Spýtať sa na dokumenty so štítkom "{tag}"...',
    chat_about_document: 'Chatovať o {document}...',
    ask_about_document: 'Spýtať sa na {document}...',
    chat_about_documents: 'Chatovať o dokumentoch...',
    ask_about_documents: 'Spýtať sa na dokumenty...',
    documents_with_tags: 'dokumenty so štítkami: {tags}',
    ask_about_documents_with_tags: 'Spýtať sa na dokumenty s týmito štítkami...',
  };
}
