import 'package:flutter/material.dart';

class DynamicBottomNavigationBar extends StatelessWidget {
  final String leftButtonText;
  final String rightButtonText;

  final VoidCallback onLeftButtonPressed;
  final VoidCallback onRightButtonPressed;

  const DynamicBottomNavigationBar({
    Key? key,
    required this.leftButtonText,
    required this.rightButtonText, 
    required this.onLeftButtonPressed,
    required this.onRightButtonPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: onLeftButtonPressed,
              child: Text(leftButtonText),
            ),
          ),
          const SizedBox(width: 16.0),
          Expanded(
            child: ElevatedButton(
              onPressed: onRightButtonPressed,
              child: Text(rightButtonText),
            ),
          ),
        ],
      ),
    );
  }
}