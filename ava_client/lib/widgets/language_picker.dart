import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';
import '../localization/locales.dart';

class LanguageDropdown extends StatelessWidget {
  final String initialLang;
  final ValueChanged<String> onChanged;

  const LanguageDropdown({
    super.key,
    required this.initialLang,
    required this.onChanged,
  });

  SimpleDialogOption _buildOption(
    BuildContext context,
    String code,
    String name,
  ) {
    return SimpleDialogOption(
      onPressed: () {
        Navigator.pop(context);
        onChanged(code);
      },
      child: Text(name),
    );
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.language),
      onPressed: () {
        showDialog(
          context: context,
          builder: (context) => SimpleDialog(
            title: Text(LocaleData.select_language.getString(context)),
            children: [
              _buildOption(context, 'en', 'English'),
              _buildOption(context, 'sk', 'Slovenčina'),
              _buildOption(context, 'hu', 'Magyar'),
            ],
          ),
        );
      },
    );
  }
}
