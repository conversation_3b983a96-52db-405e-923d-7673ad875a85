import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:ava_client/localization/locales.dart';
import 'package:ava_client/services/rag_service.dart';
import 'package:ava_client/services/voice/voice_service.dart';
import 'package:ava_client/services/voice/service_locator.dart';


class ChatWidget extends StatefulWidget {
  final String? documentName;
  final String? documentId;
  final List<String>? tags;
  final String? placeholderText;
  final String? hintText;

  const ChatWidget({
    super.key,
    this.documentName,
    this.documentId,
    this.tags,
    this.placeholderText,
    this.hintText,
  });

  @override
  State<ChatWidget> createState() => _ChatWidgetState();
}

class _ChatWidgetState extends State<ChatWidget> {
  final TextEditingController _messageController = TextEditingController();
  final List<Map<String, String>> _messages = [];
  bool _isLoading = false;
  bool _isListening = false;
  bool _autoReadEnabled = false;

  @override
  void dispose() {
    if (_isListening) {
      getIt<VoiceService>().stopListening();
    }
    _messageController.dispose();
    super.dispose();
  }

  String _getPlaceholderText(BuildContext context) {
    if (widget.documentName != null) {
      return LocaleData.chat_about_document.getString(context).replaceAll('{document}', widget.documentName!);
    } else if (widget.tags?.isNotEmpty == true) {
      final tagsText = LocaleData.documents_with_tags.getString(context).replaceAll('{tags}', widget.tags!.join(", "));
      return LocaleData.chat_about_tag.getString(context).replaceAll('{tag}', tagsText);
    } else {
      return LocaleData.chat_about_documents.getString(context);
    }
  }

  String _getHintText(BuildContext context) {
    if (widget.documentName != null) {
      return LocaleData.ask_about_document.getString(context).replaceAll('{document}', widget.documentName!);
    } else if (widget.tags?.isNotEmpty == true) {
      return LocaleData.ask_about_documents_with_tags.getString(context);
    } else {
      return LocaleData.ask_about_documents.getString(context);
    }
  }

  void _startListening() async {
    final voiceService = getIt<VoiceService>();
    if (!voiceService.isInitialized) return;

    print('Current voice language: ${voiceService.currentLanguage.code}');

    setState(() {
      _isListening = true;
    });

    await voiceService.startListening(
      onResult: (text) {
        _messageController.text = text;
      },
    );

    // Auto-stop after 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      if (_isListening) _stopListening();
    });
  }

  void _stopListening() async {
    final voiceService = getIt<VoiceService>();
    await voiceService.stopListening();
    if (mounted) {
      setState(() {
        _isListening = false;
      });
    }
  }

  void _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _isLoading) return;

    setState(() {
      _messages.add({'role': 'user', 'content': message});
      _isLoading = true;
    });
    _messageController.clear();

    try {
      final answer = await RagService.askQuestion(
        question: message,
        documentId: widget.documentId,
        tags: widget.tags,
      );
      
      setState(() {
        _messages.add({'role': 'assistant', 'content': answer});
      });
      
      if (_autoReadEnabled) {
        getIt<VoiceService>().speak(answer);
      }
    } catch (e) {
      setState(() {
        _messages.add({'role': 'assistant', 'content': 'Error: $e'});
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Chat messages area
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).dividerColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _messages.isEmpty
                  ? Text(
                      widget.placeholderText ?? 
                      'Chat about ${widget.documentName ?? 'this document'}...\n\nAsk questions about the document content.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontStyle: FontStyle.italic,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _messages.length + (_isLoading ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index == _messages.length && _isLoading) {
                          return const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Center(child: CircularProgressIndicator()),
                          );
                        }
                        final message = _messages[index];
                        final isUser = message['role'] == 'user';
                        return Align(
                          alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
                          child: Container(
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: isUser 
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.surfaceVariant,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              message['content'] ?? '',
                              style: TextStyle(
                                color: isUser 
                                    ? Colors.white
                                    : Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ),
          const SizedBox(height: 12),
          // Chat input
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _messageController,
                  decoration: InputDecoration(
                    hintText: widget.hintText ?? _getHintText(context),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
              const SizedBox(width: 8),
              // Microphone button (talk)
              IconButton(
                onPressed: _isListening ? _stopListening : _startListening,
                icon: Icon(_isListening ? Icons.stop : Icons.mic),
                style: IconButton.styleFrom(
                  backgroundColor: _isListening 
                      ? Colors.red 
                      : Theme.of(context).colorScheme.secondary,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              // Speaker button (auto-read toggle)
              IconButton(
                onPressed: () {
                  setState(() {
                    _autoReadEnabled = !_autoReadEnabled;
                  });
                  if (!_autoReadEnabled) {
                    getIt<VoiceService>().stop();
                  }
                },
                icon: const Icon(Icons.volume_up),
                style: IconButton.styleFrom(
                  backgroundColor: _autoReadEnabled 
                      ? Colors.yellow 
                      : Theme.of(context).colorScheme.tertiary,
                  foregroundColor: _autoReadEnabled ? Colors.black : Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              // Send button
              IconButton(
                onPressed: _isLoading ? null : _sendMessage,
                icon: _isLoading 
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Icon(Icons.send),
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}