import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:intl/intl.dart';
import 'package:ava_client/localization/locales.dart';
import 'package:ava_client/services/chat_history_service.dart';
import 'package:ava_client/services/tag_service.dart';
import 'package:ava_client/services/document_service.dart';

class ChatHistoryList extends StatefulWidget {
  final Function(ChatSession)? onChatSelected;

  const ChatHistoryList({
    super.key,
    this.onChatSelected,
  });

  @override
  State<ChatHistoryList> createState() => _ChatHistoryListState();
}

class _ChatHistoryListState extends State<ChatHistoryList> {
  List<ChatSession>? _chatSessions;
  Map<String, Map<String, dynamic>> _documentsCache = {};
  Map<String, Map<String, dynamic>> _tagsCache = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadChatHistory();
  }

  Future<void> _loadChatHistory() async {
    setState(() => _isLoading = true);
    
    try {
      // Load chat sessions
      final sessions = await ChatHistoryService.getChatSessions();
      
      // Load documents and tags for caching
      await _loadDocumentsAndTags(sessions);
      
      setState(() {
        _chatSessions = sessions;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading chat history: $e');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${LocaleData.error_loading_chat_history.getString(context)}: $e')),
        );
      }
    }
  }

  Future<void> _loadDocumentsAndTags(List<ChatSession> sessions) async {
    try {
      // Load all documents and tags for caching
      final documents = await DocumentService.loadDocuments();
      final tags = await TagService.loadTags();
      
      // Create lookup maps
      _documentsCache = {
        for (var doc in documents) doc['id'].toString(): doc
      };
      
      _tagsCache = {
        for (var tag in tags) tag['name']: tag
      };
    } catch (e) {
      print('Error loading documents and tags: $e');
    }
  }

  Future<void> _deleteChatSession(String sessionId) async {
    try {
      await ChatHistoryService.deleteChatSession(sessionId);
      await _loadChatHistory(); // Refresh the list
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(LocaleData.chat_deleted.getString(context))),
        );
      }
    } catch (e) {
      print('Error deleting chat session: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${LocaleData.error_deleting_chat.getString(context)}: $e')),
        );
      }
    }
  }

  String _formatDateTime(BuildContext context, DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays == 0) {
      // Today - show time
      return DateFormat('HH:mm').format(dateTime);
    } else if (difference.inDays == 1) {
      // Yesterday
      return LocaleData.yesterday.getString(context);
    } else if (difference.inDays < 7) {
      // This week - show day name
      return DateFormat('EEEE').format(dateTime);
    } else {
      // Older - show date
      return DateFormat('MMM d').format(dateTime);
    }
  }

  String _getChatTitle(BuildContext context, ChatSession session) {
    if (session.documentId != null) {
      final doc = _documentsCache[session.documentId];
      final docName = doc?['file_name'] ?? LocaleData.unknown.getString(context);
      return '$docName • ${_formatDateTime(context, session.updatedAt)}';
    } else if (session.tags != null && session.tags!.isNotEmpty) {
      final tagName = session.tags!.first;
      return '$tagName • ${_formatDateTime(context, session.updatedAt)}';
    } else {
      return '${LocaleData.chat_history.getString(context)} • ${_formatDateTime(context, session.updatedAt)}';
    }
  }

  Color _getChatColor(ChatSession session) {
    if (session.documentId != null) {
      // Light egg white color for document chats
      return const Color(0xFFFDF6E3);
    } else if (session.tags != null && session.tags!.isNotEmpty) {
      // Use tag color if available
      final tagName = session.tags!.first;
      final tag = _tagsCache[tagName];
      if (tag != null && tag['color'] != null) {
        return Color(tag['color']).withOpacity(0.1);
      }
    }
    // Default color
    return Colors.grey.withOpacity(0.1);
  }

  Color _getChatBorderColor(ChatSession session) {
    if (session.documentId != null) {
      // Slightly darker egg white for border
      return const Color(0xFFF5E6B8);
    } else if (session.tags != null && session.tags!.isNotEmpty) {
      // Use tag color for border
      final tagName = session.tags!.first;
      final tag = _tagsCache[tagName];
      if (tag != null && tag['color'] != null) {
        return Color(tag['color']).withOpacity(0.3);
      }
    }
    // Default border color
    return Colors.grey.withOpacity(0.3);
  }

  Widget _buildChatTile(ChatSession session) {
    return Dismissible(
      key: Key(session.id),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        color: Colors.red,
        child: const Icon(
          Icons.delete,
          color: Colors.white,
          size: 24,
        ),
      ),
      confirmDismiss: (direction) async {
        return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(LocaleData.delete_chat.getString(context)),
            content: Text(LocaleData.delete_chat_confirm.getString(context)),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(LocaleData.cancel.getString(context)),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(LocaleData.delete.getString(context), style: const TextStyle(color: Colors.red)),
              ),
            ],
          ),
        ) ?? false;
      },
      onDismissed: (direction) {
        _deleteChatSession(session.id);
      },
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: Container(
          decoration: BoxDecoration(
            color: _getChatColor(session),
            border: Border.all(
              color: _getChatBorderColor(session),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            title: Text(
              _getChatTitle(context, session),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            subtitle: session.sessionName != null 
                ? Text(
                    session.sessionName!,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )
                : null,
            trailing: Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
            ),
            onTap: () {
              if (widget.onChatSelected != null) {
                widget.onChatSelected!(session);
              }
            },
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_chatSessions == null || _chatSessions!.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              LocaleData.no_chat_history.getString(context),
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              LocaleData.start_conversation.getString(context),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadChatHistory,
      child: ListView.builder(
        itemCount: _chatSessions!.length,
        itemBuilder: (context, index) {
          return _buildChatTile(_chatSessions![index]);
        },
      ),
    );
  }
}
