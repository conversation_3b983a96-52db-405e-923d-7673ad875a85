import 'package:flutter/material.dart';
import 'dart:io';

import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:intl/intl.dart';

import '../../localization/locales.dart';
import '../../services/document_service.dart';
import '../../services/tag_service.dart';

import 'document_detail.dart';

class DocumentsWidget extends StatefulWidget {
  const DocumentsWidget({super.key});

  @override
  State<DocumentsWidget> createState() => _DocumentsWidgetState();
}

class _DocumentsWidgetState extends State<DocumentsWidget> {
  List<Map<String, dynamic>>? _documents;
  bool _isLoading = false;
  bool _isSelectionMode = false;
  Set<String> _selectedFiles = {};
  List<Map<String, dynamic>>? _tags;
  String _sortBy = 'name'; // 'name', 'date'
  bool _showOnlyWithoutTags = false;

  // TODO: Add a settings page where this can be set and kept in supabase.
  final int _number_of_tags_shown_as_dots = 10;

  final String _defaultBucketName = 'documents';

  String get _userFolderPath {
    final userId = Supabase.instance.client.auth.currentUser?.id;
    return 'private/$userId';
  }

  @override
  void initState() {
    super.initState();
    _loadDocuments();
    _loadTags();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Reload tags when returning to this screen
    _loadTags();
  }

  Future<void> _loadTags() async {
    try {
      final tags = await TagService.loadTags();
      setState(() {
        _tags = tags;
      });
    } catch (e) {
      // Silent fail for tags loading
    }
  }

  Future<void> _assignTagsToFiles(List<int> tagIds) async {
    try {
      for (String fileName in _selectedFiles) {
        final doc = _documents!.firstWhere((d) => d['file_name'] == fileName);
        final documentId = doc['id'];

        // Get existing tag IDs for this document
        final docTags = doc['document_tags'] as List<dynamic>? ?? [];
        final existingTagIds = docTags
            .map((dt) => dt['tags']?['id'])
            .where((id) => id != null)
            .cast<int>()
            .toSet();

        // Handle tag assignments
        for (int tagId in tagIds) {
          if (!existingTagIds.contains(tagId)) {
            // Check if this tag was previously deleted_locally
            final deletedTag = docTags.firstWhere(
              (dt) => dt['tags']?['id'] == tagId && dt['embedding_status'] == 'deleted_locally',
              orElse: () => null,
            );
            
            if (deletedTag != null) {
              // DELETED_LOCALLY → EMBEDDED: Restore the tag
              await Supabase.instance.client
                  .from('document_tags')
                  .update({'embedding_status': 'embedded'})
                  .eq('document_id', documentId)
                  .eq('tag_id', tagId);
            } else {
              // New tag assignment
              await Supabase.instance.client.from('document_tags').insert({
                'document_id': documentId,
                'tag_id': tagId,
                'embedding_status': 'new',
              });
            }
          }
        }

        // Handle unchecked tags
        for (int existingTagId in existingTagIds) {
          if (!tagIds.contains(existingTagId)) {
            // Get the current status of this tag
            final existingDocTag = docTags.firstWhere(
              (dt) => dt['tags']?['id'] == existingTagId,
              orElse: () => null,
            );
            
            if (existingDocTag != null) {
              final currentStatus = existingDocTag['embedding_status'];
              
              if (currentStatus == 'new') {
                // If tag is new (not synchronized), delete it completely
                await Supabase.instance.client
                    .from('document_tags')
                    .delete()
                    .eq('document_id', documentId)
                    .eq('tag_id', existingTagId);
              } else if (currentStatus == 'embedded') {
                // If tag was synchronized before, mark as deleted_locally
                await Supabase.instance.client
                    .from('document_tags')
                    .update({'embedding_status': 'deleted_locally'})
                    .eq('document_id', documentId)
                    .eq('tag_id', existingTagId);
              }
              // Note: 'changed' and 'deleted_locally' tags are handled by their existing status
            }
          }
        }
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(LocaleData.tag_assigned.getString(context))),
      );

      setState(() {
        _isSelectionMode = false;
        _selectedFiles.clear();
      });

      _loadDocuments();
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error assigning tag: $e')));
    }
  }

  void _showTagSelectionDialog() {
    if (_tags == null || _tags!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(LocaleData.no_tags_found.getString(context))),
      );
      return;
    }

    // Get existing tags for selected files (exclude deleted ones)
    Set<int> existingTagIds = {};
    for (String fileName in _selectedFiles) {
      final doc = _documents!.firstWhere((d) => d['file_name'] == fileName);
      final docTags = doc['document_tags'] as List<dynamic>? ?? [];
      for (var dt in docTags) {
        if (dt['tags'] != null && dt['embedding_status'] != 'deleted_locally') {
          existingTagIds.add(dt['tags']['id']);
        }
      }
    }

    Set<int> selectedTagIds = Set.from(existingTagIds);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(LocaleData.select_tag.getString(context)),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView(
              children: _tags!.map((tag) {
                final tagColor = tag['color'] != null
                    ? Color(tag['color'])
                    : Theme.of(context).colorScheme.primary;
                final isExisting = existingTagIds.contains(tag['id']);
                return CheckboxListTile(
                  secondary: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: tagColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  title: Row(
                    children: [
                      Expanded(
                        child: Text(
                          tag['name'] ?? LocaleData.no_name.getString(context),
                        ),
                      ),
                      if (isExisting)
                        const Icon(
                          Icons.check_circle,
                          size: 16,
                          color: Colors.green,
                        ),
                    ],
                  ),
                  value: selectedTagIds.contains(tag['id']),
                  onChanged: (bool? value) {
                    setState(() {
                      if (value == true) {
                        selectedTagIds.add(tag['id']);
                      } else {
                        selectedTagIds.remove(tag['id']);
                      }
                    });
                  },
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(LocaleData.cancel.getString(context)),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _assignTagsToFiles(selectedTagIds.toList());
              },
              child: Text(LocaleData.assign_tag.getString(context)),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return LocaleData.unknown.getString(context);
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('yyyy-MM-dd HH:mm').format(date);
    } catch (e) {
      return LocaleData.unknown.getString(context);
    }
  }

  List<Widget> _buildTagIndicators(Map<String, dynamic> document) {
    final documentTags = document['document_tags'] as List<dynamic>? ?? [];
    final tags = documentTags
        .map((dt) => dt['tags'])
        .where((tag) => tag != null)
        .toList();

    if (tags.isEmpty) return [];

    if (tags.length <= _number_of_tags_shown_as_dots) {
      // Show colored dots
      return tags.asMap().entries.map<Widget>((entry) {
        final tag = entry.value;
        final docTag = documentTags[entry.key];
        final status = docTag['embedding_status'];
        final isEmbedded = status == 'embedded';
        final isDeleted = status == 'deleted_locally';
        final isChanged = status == 'changed';
        final color = tag['color'] != null
            ? Color(tag['color'])
            : Theme.of(context).colorScheme.primary;
        return Container(
          margin: const EdgeInsets.only(left: 4),
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: isEmbedded ? color : (isDeleted ? color : Colors.transparent),
            border: Border.all(color: color, width: 1),
            shape: BoxShape.circle,
          ),
          child: isDeleted ? Icon(Icons.close, size: 8, color: Colors.white) : null,
        );
      }).toList();
    } else {
      // Show count
      return [
        Container(
          margin: const EdgeInsets.only(left: 8),
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            '${tags.length}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ];
    }
  }

  Future<void> _loadDocuments() async {
    setState(() => _isLoading = true);

    try {
      final documents = await DocumentService.loadDocuments();
      setState(() {
        _documents = documents;
      });
    } catch (e) {
      print("==================> Error loading documents: $e");
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading documents: $e')));
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _markDocumentAsEmbedded(String fileName) async {
    try {
      await DocumentService.processDocumentByFileName(fileName, _documents!);
      _loadDocuments();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error marking document as embedded: $e')),
      );
    }
  }

  Future<void> _deleteSelectedDocuments() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocaleData.delete_documents.getString(context)),
        content: Text(LocaleData.delete_documents_confirm.getString(context).replaceAll('{count}', '${_selectedFiles.length}')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(LocaleData.cancel.getString(context)),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(LocaleData.delete.getString(context)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      for (String fileName in _selectedFiles) {
        final doc = _documents!.firstWhere((d) => d['file_name'] == fileName);
        await DocumentService.deleteDocumentById(doc['id']);
      }
      setState(() {
        _isSelectionMode = false;
        _selectedFiles.clear();
      });
      _loadDocuments();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting documents: $e')),
      );
    }
  }

  Future<void> _markSelectedDocumentsAsEmbedded() async {
    final alreadyProcessedFiles = _selectedFiles.where((fileName) {
      final doc = _documents!.firstWhere(
        (d) => d['file_name'] == fileName,
        orElse: () => <String, dynamic>{},
      );
      if (doc.isEmpty) return false;
      final status = doc['embedding_status'];
      return status == 'completed';
    }).toList();

    if (alreadyProcessedFiles.isNotEmpty) {
      final shouldProceed = await _showReprocessDialog();
      if (shouldProceed != true) return;
    }

    try {
      // Use DocumentService + sync function
      await DocumentService.processMultipleDocuments(_selectedFiles.toList(), _documents!);
      
      // Sync each selected document
      for (String fileName in _selectedFiles) {
        final doc = _documents!.firstWhere(
          (d) => d['file_name'] == fileName,
          orElse: () => <String, dynamic>{},
        );
        if (doc.isNotEmpty) {
          await DocumentService.syncDocumentToEmbedding(doc['id']);
        }
      }
      
      setState(() {
        _isSelectionMode = false;
        _selectedFiles.clear();
      });
      _loadDocuments();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error marking documents as embedded: $e')),
      );
    }
  }

  Future<bool?> _showReprocessDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocaleData.already_processed.getString(context)),
        content: Text(LocaleData.reprocess_warning.getString(context)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(LocaleData.cancel.getString(context)),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(LocaleData.proceed.getString(context)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteDocument(String fileName) async {
    try {
      print("---> About to delete document $fileName...");
      final doc = _documents!.firstWhere((d) => d['file_name'] == fileName);
      await DocumentService.deleteDocumentById(doc['id']);
      print("---> Document $fileName deleted, calling _loadDocuments()");
      _loadDocuments();
    } catch (e) {
      print("---> Error deleting document: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '${LocaleData.error_deleting_file.getString(context)}: $e',
          ),
        ),
      );
    }
  }

  Future<void> _uploadDocument() async {
    final result = await FilePicker.platform.pickFiles();

    if (result != null && result.files.single.path != null) {
      try {
        final file = File(result.files.single.path!);
        final fileName = result.files.single.name;

        // Validate filename: no spaces, underscore allowed, English letters and numbers only
        if (!RegExp(r'^[a-zA-Z0-9_.]+$').hasMatch(fileName)) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(LocaleData.invalid_filename.getString(context)),
            ),
          );
          return;
        }

        await DocumentService.uploadDocument(file, fileName);
        _loadDocuments();
      } catch (e) {
        print("---> Error uploading document: $e");

        String errorMessage;
        if (e.toString().contains('duplicate key') ||
            e.toString().contains('unique constraint')) {
          errorMessage = LocaleData.file_already_exists.getString(context);
        } else {
          errorMessage = LocaleData.error_uploading_file.getString(context);
        }
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(errorMessage)));
      }
    }
  }

  List<Map<String, dynamic>> _getSortedDocuments() {
    if (_documents == null) return [];
    
    var filteredDocs = List<Map<String, dynamic>>.from(_documents!);
    
    // Filter by tags if checkbox is checked
    if (_showOnlyWithoutTags) {
      filteredDocs = filteredDocs.where((doc) {
        final docTags = doc['document_tags'] as List<dynamic>? ?? [];
        return docTags.isNotEmpty;
      }).toList();
    }
    
    // Sort the filtered documents
    switch (_sortBy) {
      case 'name':
        filteredDocs.sort((a, b) => (a['file_name'] ?? '').compareTo(b['file_name'] ?? ''));
        break;
      case 'date':
        filteredDocs.sort((a, b) {
          final aDate = DateTime.tryParse(a['created_at'] ?? '') ?? DateTime(1970);
          final bDate = DateTime.tryParse(b['created_at'] ?? '') ?? DateTime(1970);
          return bDate.compareTo(aDate); // Newest first
        });
        break;
    }
    
    return filteredDocs;
  }

  Color? _getDocumentBackgroundColor(Map<String, dynamic> document) {
    final status = document['embedding_status'];
    if (status != 'completed') return null; // White for unprocessed
    
    final documentTags = document['document_tags'] as List<dynamic>? ?? [];
    
    // Check for any tags that are out of sync with vector DB
    final hasChangedTags = documentTags.any((dt) => 
      dt['embedding_status'] == 'new' || 
      dt['embedding_status'] == 'changed' ||
      dt['embedding_status'] == 'deleted_locally'
    );
    
    if (hasChangedTags) {
      return Colors.orange.withOpacity(0.2); // Light orange for processed with changes
    } else {
      return Colors.lightGreen.withOpacity(0.2); // Light green for fully processed
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _isSelectionMode
          ? AppBar(
              leading: IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _isSelectionMode = false;
                    _selectedFiles.clear();
                  });
                },
              ),
              title: Text(
                '${_selectedFiles.length} ${LocaleData.selected.getString(context)}',
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      if (_selectedFiles.length == _documents!.length) {
                        _selectedFiles.clear();
                      } else {
                        _selectedFiles = _documents!
                            .map((doc) => doc['file_name'] as String)
                            .toSet();
                      }
                    });
                  },
                  child: Text(
                    _selectedFiles.length == _documents!.length
                        ? LocaleData.deselect_all.getString(context)
                        : LocaleData.select_all.getString(context),
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.local_offer),
                  onPressed: _selectedFiles.isNotEmpty
                      ? _showTagSelectionDialog
                      : null,
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: _selectedFiles.isNotEmpty
                      ? _deleteSelectedDocuments
                      : null,
                ),
                IconButton(
                  icon: const Icon(Icons.settings_suggest_sharp),
                  onPressed: _selectedFiles.isNotEmpty
                      ? _markSelectedDocumentsAsEmbedded
                      : null,
                ),
              ],
            )
          : AppBar(
              title: Text(LocaleData.documents.getString(context)),
              actions: [
                PopupMenuButton<String>(
                  icon: const Icon(Icons.sort),
                  onSelected: (value) {
                    setState(() {
                      _sortBy = value;
                    });
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'name',
                      child: Text(LocaleData.sort_by_name.getString(context)),
                    ),
                    PopupMenuItem(
                      value: 'date',
                      child: Text(LocaleData.sort_by_date.getString(context)),
                    ),
                    PopupMenuItem(
                      value: null,
                      child: StatefulBuilder(
                        builder: (context, setState) => CheckboxListTile(
                          title: Text(LocaleData.with_tags.getString(context)),
                          value: _showOnlyWithoutTags,
                          onChanged: (value) {
                            setState(() {
                              _showOnlyWithoutTags = value ?? false;
                            });
                            this.setState(() {});
                          },
                          controlAffinity: ListTileControlAffinity.leading,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
      body: RefreshIndicator(
        onRefresh: _loadDocuments,
        child: _documents == null
            ? const Center(child: CircularProgressIndicator())
            : _documents!.isEmpty
            ? Center(
                child: Text(LocaleData.no_documents_found.getString(context)),
              )
            : ListView.builder(
                itemCount: _getSortedDocuments().length,
                itemBuilder: (context, index) {
                  final document = _getSortedDocuments()[index];
                  final fileName = document['file_name'] ?? '';
                  return Dismissible(
                    key: Key(fileName),
                    direction: _isSelectionMode 
                        ? DismissDirection.none 
                        : DismissDirection.horizontal,
                    confirmDismiss: _isSelectionMode
                        ? null
                        : (direction) async {
                            if (direction == DismissDirection.startToEnd) {
                              // Swipe right - mark as embedded
                              final status = document['embedding_status'];
                              final isAlreadyProcessed = status == 'completed';

                              if (isAlreadyProcessed) {
                                final shouldProceed =
                                    await _showReprocessDialog();
                                if (shouldProceed == true) {
                                  await _markDocumentAsEmbedded(fileName);
                                }
                              } else {
                                await _markDocumentAsEmbedded(fileName);
                                showDialog(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    content: Text(
                                      LocaleData.embedding_document.getString(
                                        context,
                                      ),
                                    ),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Navigator.pop(context),
                                        child: Text(LocaleData.ok.getString(context)),
                                      ),
                                    ],
                                  ),
                                );
                              }
                              return false;
                            } else {
                              // Swipe left - delete
                              return await showDialog<bool>(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      title: Text(
                                        LocaleData.delete_document.getString(
                                          context,
                                        ),
                                      ),
                                      content: Text(
                                        LocaleData.delete_document_confirm
                                            .getString(context),
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.pop(context, false),
                                          child: Text(
                                            LocaleData.cancel.getString(
                                              context,
                                            ),
                                          ),
                                        ),
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.pop(context, true),
                                          child: Text(
                                            LocaleData.delete.getString(
                                              context,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ) ??
                                  false;
                            }
                          },
                    onDismissed: (direction) async {
                      if (direction == DismissDirection.endToStart) {
                        final doc = document;
                        // Remove from list immediately to prevent error
                        setState(() {
                          _documents!.removeWhere((d) => d['file_name'] == fileName);
                        });
                        // Delete using document ID for proper Qdrant cleanup
                        try {
                          await DocumentService.deleteDocumentById(doc['id']);
                        } catch (e) {
                          // If delete fails, reload documents to restore the list
                          _loadDocuments();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Error deleting document: $e')),
                          );
                        }
                      }
                    },
                    background: Container(
                      color: Colors.green,
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.only(left: 20),
                      child: const Icon(
                        Icons.settings_suggest_sharp,
                        color: Colors.white,
                      ),
                    ),
                    secondaryBackground: Container(
                      color: Colors.red,
                      alignment: Alignment.centerRight,
                      padding: const EdgeInsets.only(right: 20),
                      child: const Icon(Icons.delete, color: Colors.white),
                    ),
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getDocumentBackgroundColor(document),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context).dividerColor,
                          width: 1,
                        ),
                      ),
                      child: ListTile(
                        leading: _isSelectionMode
                            ? Checkbox(
                                value: _selectedFiles.contains(fileName),
                                onChanged: (bool? value) {
                                  setState(() {
                                    if (value == true) {
                                      _selectedFiles.add(fileName);
                                    } else {
                                      _selectedFiles.remove(fileName);
                                    }
                                  });
                                },
                              )
                            : null,
                        title: Row(
                          children: [
                            Expanded(
                              child: Text(
                                document['file_name'] ??
                                    LocaleData.no_name.getString(context),
                              ),
                            ),
                            ..._buildTagIndicators(document),
                          ],
                        ),
                        subtitle: Text(
                          '${LocaleData.uploaded.getString(context)}: ${_formatDate(document['created_at'])}',
                        ),
                        onLongPress: () {
                          setState(() {
                            _isSelectionMode = true;
                            _selectedFiles.add(fileName);
                          });
                        },
                        onTap: _isSelectionMode
                            ? () {
                                setState(() {
                                  if (_selectedFiles.contains(fileName)) {
                                    _selectedFiles.remove(fileName);
                                    if (_selectedFiles.isEmpty) {
                                      _isSelectionMode = false;
                                    }
                                  } else {
                                    _selectedFiles.add(fileName);
                                  }
                                });
                              }
                            : () async {
                                final result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => DocumentDetailScreen(
                                      document: document,
                                    ),
                                  ),
                                );
                                if (result == true) {
                                  _loadDocuments();
                                }
                              },
                      ),
                    ),
                  );
                },
              ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _uploadDocument,
        child: const Icon(Icons.add),
      ),
    );
  }
}
