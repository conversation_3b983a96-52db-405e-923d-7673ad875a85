import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../localization/locales.dart';
import '../../services/document_service.dart';
import '../../services/tag_service.dart';
import '../../widgets/chat_widget.dart';

class DocumentDetailScreen extends StatefulWidget {
  final Map<String, dynamic> document;

  const DocumentDetailScreen({super.key, required this.document});

  @override
  State<DocumentDetailScreen> createState() => _DocumentDetailScreenState();
}

class _DocumentDetailScreenState extends State<DocumentDetailScreen> {
  List<Map<String, dynamic>>? _tags;
  Map<String, dynamic>? _document;

  @override
  void initState() {
    super.initState();
    _document = widget.document;
    _loadTags();
  }

  Future<void> _loadTags() async {
    try {
      final tags = await TagService.loadTags();
      setState(() {
        _tags = tags;
      });
    } catch (e) {
      print("-----> Error at _loadTags: $e}");
    }
  }

  Future<void> _assignTagsToDocument(List<int> tagIds) async {
    try {
      final documentId = _document!['id'];

      // Get existing tag IDs for this document
      final docTags = _document!['document_tags'] as List<dynamic>? ?? [];
      final existingTagIds = docTags
          .map((dt) => dt['tags']?['id'])
          .where((id) => id != null)
          .cast<int>()
          .toSet();

      // Only insert new tags
      for (int tagId in tagIds) {
        if (!existingTagIds.contains(tagId)) {
          await Supabase.instance.client.from('document_tags').upsert({
            'document_id': documentId,
            'tag_id': tagId,
            'embedding_status': 'new',
          });
        }
      }

      // Handle unchecked tags
      for (int existingTagId in existingTagIds) {
        if (!tagIds.contains(existingTagId)) {
          // Get the current status of this tag
          final existingDocTag = docTags.firstWhere(
            (dt) => dt['tags']?['id'] == existingTagId,
            orElse: () => null,
          );
          
          if (existingDocTag != null) {
            final currentStatus = existingDocTag['embedding_status'];
            
            if (currentStatus == 'new') {
              // If tag is new (not synchronized), delete it completely
              await Supabase.instance.client
                  .from('document_tags')
                  .delete()
                  .eq('document_id', documentId)
                  .eq('tag_id', existingTagId);
            } else if (currentStatus == 'embedded') {
              // If tag was synchronized before, mark as deleted_locally
              await Supabase.instance.client.from('document_tags').update({
                'embedding_status': 'deleted_locally',
              }).match({
                'document_id': documentId,
                'tag_id': existingTagId,
              });
            }
          }
        }
      }
      
      // Restore previously deleted tags that are now selected (keep original status)
      for (int tagId in tagIds) {
        if (existingTagIds.contains(tagId)) {
          // Get current status to restore properly
          final currentTag = docTags.firstWhere(
            (dt) => dt['tags']?['id'] == tagId,
            orElse: () => {'embedding_status': 'new'},
          );
          final currentStatus = currentTag['embedding_status'];
          
          // Only update if it was deleted_locally, otherwise keep original status
          if (currentStatus == 'deleted_locally') {
            await Supabase.instance.client.from('document_tags').update({
              'embedding_status': 'embedded', // Restore to embedded if it was deleted
            }).match({
              'document_id': documentId,
              'tag_id': tagId,
            });
          }
        }
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(LocaleData.tag_assigned.getString(context))),
      );

      // Don't change document status when tags are modified

      // Reload document data
      await _reloadDocument();
    } catch (e) {
      print("-----> Error at _assignTagsToDocument: $e}");
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error assigning tag: $e')));
    }
  }

  Future<void> _reloadDocument() async {
    try {
      final document = await DocumentService.loadDocument(_document!['id']);
      setState(() {
        _document = document;
      });
    } catch (e) {
      print("-----> Error at _reloadDocument: $e}");
    }
  }

  void _showTagSelectionDialog() {
    if (_tags == null || _tags!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(LocaleData.no_tags_found.getString(context))),
      );
      return;
    }

    // Get existing tags for this document (exclude deleted ones)
    final docTags = _document!['document_tags'] as List<dynamic>? ?? [];
    Set<int> existingTagIds = {};
    for (var dt in docTags) {
      if (dt['tags'] != null && dt['embedding_status'] != 'deleted_locally') {
        existingTagIds.add(dt['tags']['id']);
      }
    }

    Set<int> selectedTagIds = Set.from(existingTagIds);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(LocaleData.select_tag.getString(context)),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView(
              children: _tags!.map((tag) {
                final tagColor = tag['color'] != null
                    ? Color(tag['color'])
                    : Theme.of(context).colorScheme.primary;
                final isExisting = existingTagIds.contains(tag['id']);
                return CheckboxListTile(
                  secondary: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: tagColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  title: Row(
                    children: [
                      Expanded(
                        child: Text(
                          tag['name'] ?? LocaleData.no_name.getString(context),
                        ),
                      ),
                      if (isExisting)
                        const Icon(
                          Icons.check_circle,
                          size: 16,
                          color: Colors.green,
                        ),
                    ],
                  ),
                  value: selectedTagIds.contains(tag['id']),
                  onChanged: (bool? value) {
                    setState(() {
                      if (value == true) {
                        selectedTagIds.add(tag['id']);
                      } else {
                        selectedTagIds.remove(tag['id']);
                      }
                    });
                  },
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(LocaleData.cancel.getString(context)),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _assignTagsToDocument(selectedTagIds.toList());
              },
              child: Text(LocaleData.assign_tag.getString(context)),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context, true),
        ),
        title: Text(
          _document?['file_name'] ?? LocaleData.unknown.getString(context),
          overflow: TextOverflow.ellipsis,
        ),
        actions: [
          IconButton(
            onPressed: _markDocumentAsEmbedded,
            icon: const Icon(Icons.settings_suggest_sharp),
          ),
        ],
      ),
      body: Column(
        children: [
          // Header Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Tags and status row combined
                GestureDetector(
                  onTap: _showTagSelectionDialog,
                  child: Row(
                    children: [
                      Expanded(child: _buildTagsDisplay(context)),
                      const SizedBox(width: 8),
                      _buildStatusIndicator(context),
                    ],
                  ),
                ),
              ],
            ),
          ),



          // Chat Widget - only active when document is processed
          Expanded(
            child: SafeArea(
              child: _isDocumentProcessed()
                  ? ChatWidget(
                      documentName: _document?['file_name'],
                      documentId: _document?['id']?.toString(),
                    )
                  : _buildChatPlaceholder(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsDisplay(BuildContext context) {
    final documentTags = _document?['document_tags'] as List<dynamic>? ?? [];
    final tags = documentTags
        .map((dt) => dt['tags'])
        .where((tag) => tag != null)
        .toList();

    if (tags.isEmpty) {
      return Text(
        LocaleData.no_tags.getString(context),
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontStyle: FontStyle.italic,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      );
    }

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: tags.map<Widget>((tag) {
        final color = tag['color'] != null
            ? Color(tag['color'])
            : Theme.of(context).colorScheme.primary;

        final docTag = documentTags.firstWhere(
          (dt) => dt['tags']?['id'] == tag['id'],
          orElse: () => {'embedding_status': 'new'},
        );
        final status = docTag['embedding_status'];
        final isEmbedded = status == 'embedded';
        final isDeleted = status == 'deleted_locally';
        final isChanged = status == 'changed';

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: (isEmbedded && !isDeleted) ? color : color.withValues(alpha: 0.1),
            border: Border.all(color: color),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            tag['name'] ?? 'Unknown',
            style: TextStyle(
              color: (isEmbedded && !isDeleted) ? Colors.white : color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              decoration: isDeleted ? TextDecoration.lineThrough : null,
              fontStyle: isChanged ? FontStyle.italic : null,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildStatusIndicator(BuildContext context) {
    final status = _document?['embedding_status'];
    final isCompleted = status == 'completed';
    Color color;
    String text;
    Color textColor;

    if (isCompleted) {
      color = Colors.green;
      text = LocaleData.processed.getString(context);
      textColor = Colors.black;
    } else {
      color = Colors.orange;
      text = LocaleData.not_processed.getString(context);
      textColor = Colors.white;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: CustomPaint(
        painter: StripedBackgroundPainter(color: color, isCompleted: isCompleted),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Text(
            text,
            style: TextStyle(
              color: textColor,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _markDocumentAsEmbedded() async {
    final status = _document?['embedding_status'];
    final isAlreadyProcessed = status == 'completed';

    if (isAlreadyProcessed) {
      final shouldProceed = await _showReprocessDialog();
      if (shouldProceed != true) return;
    }

    try {
      // Use DocumentService + sync function
      await DocumentService.processDocument(_document!['id']);
      await DocumentService.syncDocumentToEmbedding(_document!['id']);
      
      await _reloadDocument();

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          content: Text(LocaleData.embedding_document.getString(context)),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(LocaleData.ok.getString(context)),
            ),
          ],
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error marking document as embedded: $e')),
      );
    }
  }

  bool _isDocumentProcessed() {
    final status = _document?['embedding_status'];
    return status == 'completed';
  }

  Widget _buildChatPlaceholder(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withAlpha((0.3 * 255).round()),
          ),
          const SizedBox(height: 16),
          Text(
            LocaleData.document_must_be_processed.getString(context),
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withAlpha((0.6 * 255).round()),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            LocaleData.click_process_button.getString(context),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withAlpha((0.4 * 255).round()),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<bool?> _showReprocessDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocaleData.already_processed.getString(context)),
        content: Text(LocaleData.reprocess_warning.getString(context)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(LocaleData.cancel.getString(context)),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(LocaleData.proceed.getString(context)),
          ),
        ],
      ),
    );
  }

}

class StripedBackgroundPainter extends CustomPainter {
  final Color color;
  final bool isCompleted;

  StripedBackgroundPainter({required this.color, required this.isCompleted});

  @override
  void paint(Canvas canvas, Size size) {
    final rect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      const Radius.circular(12),
    );

    // Create a clipping path for rounded corners
    canvas.clipRRect(rect);

    if (isCompleted) {
      // For "Processed" status: Green and white stripes with faded green
      const stripeWidth = 3.0;
      const stripeSpacing = 6.0;

      // Faded green color
      final fadedGreen = color.withValues(alpha: 0.7);

      // Draw white background first
      final whitePaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;
      canvas.drawRRect(rect, whitePaint);

      // Draw faded green diagonal stripes
      final stripePaint = Paint()
        ..color = fadedGreen
        ..style = PaintingStyle.fill;

      for (double i = -size.height; i < size.width + size.height; i += stripeSpacing) {
        final path = Path();
        path.moveTo(i, 0);
        path.lineTo(i + stripeWidth, 0);
        path.lineTo(i + stripeWidth + size.height, size.height);
        path.lineTo(i + size.height, size.height);
        path.close();
        canvas.drawPath(path, stripePaint);
      }
    } else {
      // For "Not Processed" status: Solid orange background with darker stripes
      final paint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      // Draw the orange background
      canvas.drawRRect(rect, paint);

      // Draw darker diagonal stripes
      final stripePaint = Paint()
        ..color = color.withValues(alpha: 0.3)
        ..style = PaintingStyle.fill;

      const stripeWidth = 3.0;
      const stripeSpacing = 6.0;

      for (double i = -size.height; i < size.width + size.height; i += stripeSpacing) {
        final path = Path();
        path.moveTo(i, 0);
        path.lineTo(i + stripeWidth, 0);
        path.lineTo(i + stripeWidth + size.height, size.height);
        path.lineTo(i + size.height, size.height);
        path.close();
        canvas.drawPath(path, stripePaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! StripedBackgroundPainter ||
           oldDelegate.color != color ||
           oldDelegate.isCompleted != isCompleted;
  }
}
