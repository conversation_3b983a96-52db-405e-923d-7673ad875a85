import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:ava_client/localization/locales.dart';
import 'package:ava_client/widgets/chat_history_list.dart';
import 'package:ava_client/widgets/chat_widget.dart';
import 'package:ava_client/services/chat_history_service.dart';

class ChatHistoryScreen extends StatefulWidget {
  const ChatHistoryScreen({super.key});

  @override
  State<ChatHistoryScreen> createState() => _ChatHistoryScreenState();
}

class _ChatHistoryScreenState extends State<ChatHistoryScreen> {
  ChatSession? _selectedSession;

  void _onChatSelected(ChatSession session) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatSessionScreen(session: session),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chat History'),
        leading: <PERSON><PERSON><PERSON><PERSON><PERSON>(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: ChatHistoryList(
        onChatSelected: _onChatSelected,
      ),
    );
  }
}

class ChatSessionScreen extends StatelessWidget {
  final ChatSession session;

  const ChatSessionScreen({
    super.key,
    required this.session,
  });

  String _getDocumentName(ChatSession session) {
    if (session.documentId != null) {
      // We could load the document name here, but for now use a placeholder
      return 'Document Chat';
    } else if (session.tags != null && session.tags!.isNotEmpty) {
      return session.tags!.first;
    }
    return 'Chat';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getDocumentName(session)),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: ChatWidget(
        existingSession: session,
        documentId: session.documentId,
        tags: session.tags,
        documentName: _getDocumentName(session),
      ),
    );
  }
}
