import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:ava_client/localization/locales.dart';
import 'package:ava_client/screens/screen_home.dart';
import 'package:ava_client/widgets/language_picker.dart';
import 'package:ava_client/auth/secrets.dart' as sec;

// --------------------------------------------------------------------------------
class AuthPage extends StatefulWidget {
  final Function(String) onLanguageChanged;
  
  const AuthPage({super.key, required this.onLanguageChanged});

  @override
  State<AuthPage> createState() => _AuthPageState();
}

// --------------------------------------

class _AuthPageState extends State<AuthPage> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLogin = true;
  bool _isLoading = false;

  Future<void> _authenticate() async {
    setState(() => _isLoading = true);
    
    try {
      // Authenticate with Supabase first
      final AuthResponse response;
      if (_isLogin) {
        response = await Supabase.instance.client.auth.signInWithPassword(
          email: _emailController.text,
          password: _passwordController.text,
        );
      } else {
        response = await Supabase.instance.client.auth.signUp(
          email: _emailController.text,
          password: _passwordController.text,
        );
      }
      
      if (response.user != null) {
        // Store Supabase token for FastAPI calls
        sec.Tokens.accessToken = response.session?.accessToken;
        sec.Tokens.refreshToken = response.session?.refreshToken;
        
        // Navigate to home
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => HomeScreen(onLanguageChanged: widget.onLanguageChanged)),
        );
      } else {
        throw Exception('Authentication failed');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${LocaleData.error.getString(context)}: $e')),
      );
    }
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isLogin ? LocaleData.login.getString(context) : LocaleData.signup.getString(context)),
        actions: [
          LanguageDropdown(
            initialLang: 'en',
            onChanged: widget.onLanguageChanged,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _emailController,
              decoration:  InputDecoration(labelText: LocaleData.email.getString(context)),
            ),
            TextField(
              controller: _passwordController,
              decoration: InputDecoration(labelText: LocaleData.password.getString(context)),
              obscureText: true,
            ),
            const SizedBox(height: 20),
            
            _isLoading
                ? const CircularProgressIndicator()
                : ElevatedButton(
                    onPressed: _authenticate,
                    child: Text(_isLogin ? LocaleData.login.getString(context) : LocaleData.signup.getString(context)),
                  ),
            
            TextButton(
              onPressed: () => setState(() => _isLogin = !_isLogin),
              child: Text(_isLogin ? LocaleData.signup_message.getString(context) : LocaleData.login_message.getString(context)),
            ),
          ],
        ),
      ),
    );
  }
}