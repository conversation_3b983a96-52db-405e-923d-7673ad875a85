import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:intl/intl.dart';
import '../../localization/locales.dart';
import '../../services/document_service.dart';
import '../../widgets/chat_widget.dart';
import '../documents/document_detail.dart';

class TagDetailScreen extends StatefulWidget {
  final Map<String, dynamic> tag;

  const TagDetailScreen({super.key, required this.tag});

  @override
  State<TagDetailScreen> createState() => _TagDetailScreenState();
}

class _TagDetailScreenState extends State<TagDetailScreen> {
  List<Map<String, dynamic>>? _documents;
  bool _isLoading = false;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadDocuments();
  }

  Future<void> _loadDocuments() async {
    setState(() => _isLoading = true);
    try {
      final allDocuments = await DocumentService.loadDocuments();
      final filteredDocuments = allDocuments.where((doc) {
        final docTags = doc['document_tags'] as List<dynamic>? ?? [];
        return docTags.any((dt) => 
          dt['tags']?['id'] == widget.tag['id'] && 
          dt['embedding_status'] == 'embedded'
        );
      }).toList();
      setState(() {
        _documents = filteredDocuments;
      });
    } catch (e) {
      print('Error loading documents: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }
  @override
  Widget build(BuildContext context) {
    final color = widget.tag['color'] != null
        ? Color(widget.tag['color'])
        : Theme.of(context).colorScheme.primary;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        title: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            widget.tag['name'] ?? LocaleData.unknown_tag.getString(context),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(left: 16, right: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).dividerColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Theme(
                data: Theme.of(context).copyWith(
                  dividerColor: Colors.transparent,
                ),
                child: ExpansionTile(
                  tilePadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                  childrenPadding: EdgeInsets.zero,
                  title: Text(
                    LocaleData.documents_with_tag.getString(context).replaceAll('{count}', '${_documents?.length ?? 0}'),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  initiallyExpanded: false,
                children: _isLoading
                    ? [const Center(child: CircularProgressIndicator())]
                    : _documents == null || _documents!.isEmpty
                        ? [
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: Text(
                                LocaleData.no_documents_found.getString(context),
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontStyle: FontStyle.italic,
                                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                            ),
                          ]
                        : _documents!.map((document) {
                            return ListTile(
                              dense: true,
                              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                              minVerticalPadding: 0,
                              title: Text(
                                document['file_name'] ?? LocaleData.no_name.getString(context),
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              onTap: () async {
                                final result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => DocumentDetailScreen(document: document),
                                  ),
                                );
                                if (result == true) {
                                  _loadDocuments();
                                }
                              },
                            );
                          }).toList(),
                ),
              ),
            ),
            const SizedBox(height: 2),
            Expanded(
              child: ChatWidget(
                tags: [widget.tag['name']],
                placeholderText:
                    LocaleData.chat_about_tag.getString(context).replaceAll('{tag}', widget.tag['name'] ?? ''),
                hintText:
                    LocaleData.ask_about_tag.getString(context).replaceAll('{tag}', widget.tag['name'] ?? ''),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
