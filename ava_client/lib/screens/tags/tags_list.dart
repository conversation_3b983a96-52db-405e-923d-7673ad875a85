import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:ava_client/localization/locales.dart';
import 'tag_detail.dart';

class TagsWidget extends StatefulWidget {
  const TagsWidget({super.key});

  @override
  State<TagsWidget> createState() => _TagsWidgetState();
}

class _TagsWidgetState extends State<TagsWidget> {
  List<Map<String, dynamic>>? _tags;
  bool _isLoading = false;
  int? _selectedTagId;

  @override
  void initState() {
    super.initState();
    _loadTags();
  }

  Future<void> _loadTags() async {
    setState(() => _isLoading = true);
    
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');
      
      final response = await Supabase.instance.client
          .from('tags')
          .select()
          .eq('user_id', userId);
      
      setState(() {
        _tags = List<Map<String, dynamic>>.from(response);
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${LocaleData.error_loading_tags.getString(context)}: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _createTag(String name, int colorValue) async {
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      await Supabase.instance.client
          .from('tags')
          .insert({'name': name, 'user_id': userId, 'color': colorValue});
      
      _loadTags();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${LocaleData.error_creating_tag.getString(context)}: $e')),
      );
    }
  }

  Future<void> _updateTag(int id, String name, int colorValue) async {
    try {
      await Supabase.instance.client
          .from('tags')
          .update({'name': name, 'color': colorValue})
          .eq('id', id);
      
      _loadTags();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${LocaleData.error_updating_tag.getString(context)}: $e')),
      );
    }
  }

  Future<void> _deleteTag(int id) async {
    try {
      // Check if tag has linked documents
      final docCount = await Supabase.instance.client
          .from('document_tags')
          .select('id')
          .eq('tag_id', id)
          .count();
      
      if (docCount.count > 0) {
        final confirmed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(LocaleData.delete_tag.getString(context)),
            content: Text(LocaleData.delete_tag_confirm.getString(context).replaceAll('{count}', '${docCount.count}')),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(LocaleData.cancel.getString(context)),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text(LocaleData.delete.getString(context)),
              ),
            ],
          ),
        );
        
        if (confirmed != true) return;
      }
      
      await Supabase.instance.client
          .from('tags')
          .delete()
          .eq('id', id);
      
      _loadTags();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${LocaleData.error_deleting_tag.getString(context)}: $e')),
      );
    }
  }

  void _showTagDialog({int? id, String? currentName, int? currentColor}) {
    final isEditing = id != null;
    final controller = TextEditingController(text: currentName ?? '');
    final defaultColor = currentColor != null ? Color(currentColor) : Theme.of(context).colorScheme.primary;
    Color selectedColor = defaultColor;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(isEditing ? LocaleData.edit_tag.getString(context) : LocaleData.create_tag.getString(context)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: controller,
                maxLength: 50,
                decoration: InputDecoration(
                  hintText: LocaleData.tag_name.getString(context),
                  counterText: '',
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Text('${LocaleData.color.getString(context)}: '),
                  GestureDetector(
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: Text(LocaleData.pick_color.getString(context)),
                          content: SingleChildScrollView(
                            child: BlockPicker(
                              pickerColor: selectedColor,
                              onColorChanged: (color) {
                                setState(() {
                                  selectedColor = color;
                                });
                                Navigator.pop(context);
                              },
                            ),
                          ),
                        ),
                      );
                    },
                    child: Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: selectedColor,
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(LocaleData.cancel.getString(context)),
            ),
            TextButton(
              onPressed: () {
                if (controller.text.isNotEmpty) {
                  if (isEditing) {
                    _updateTag(id!, controller.text, selectedColor.toARGB32());
                  } else {
                    _createTag(controller.text, selectedColor.toARGB32());
                  }
                }
                Navigator.pop(context);
              },
              child: Text(isEditing ? LocaleData.update.getString(context) : LocaleData.create.getString(context)),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleData.tags.getString(context)),
      ),
      body: RefreshIndicator(
        onRefresh: _loadTags,
        child: _tags == null
            ? const Center(child: CircularProgressIndicator())
            : _tags!.isEmpty
                ? Center(child: Text(LocaleData.no_tags_found.getString(context)))
                : Padding(
                    padding: const EdgeInsets.all(16),
                    child: Wrap(
                      spacing: 12,
                      runSpacing: 12,
                      children: _tags!.map((tag) {
                        final tagColor = tag['color'] != null 
                            ? Color(tag['color']) 
                            : Theme.of(context).colorScheme.primary;
                        final isSelected = _selectedTagId == tag['id'];
                        
                        return GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => TagDetailScreen(tag: tag),
                              ),
                            );
                          },
                          onLongPress: () {
                            setState(() {
                              _selectedTagId = isSelected ? null : tag['id'];
                            });
                          },
                          child: Stack(
                            clipBehavior: Clip.none,
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                decoration: BoxDecoration(
                                  color: tagColor.withOpacity(0.1),
                                  border: Border.all(color: tagColor, width: 2),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  tag['name'] ?? LocaleData.no_name.getString(context),
                                  style: TextStyle(
                                    color: tagColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              if (isSelected)
                                Positioned(
                                  top: -12,
                                  right: -12,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          _showTagDialog(
                                            id: tag['id'],
                                            currentName: tag['name'],
                                            currentColor: tag['color'],
                                          );
                                          setState(() => _selectedTagId = null);
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: const BoxDecoration(
                                            color: Colors.blue,
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.edit,
                                            color: Colors.white,
                                            size: 18,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      GestureDetector(
                                        onTap: () {
                                          _deleteTag(tag['id']);
                                          setState(() => _selectedTagId = null);
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: const BoxDecoration(
                                            color: Colors.red,
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.remove,
                                            color: Colors.white,
                                            size: 18,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showTagDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }
}