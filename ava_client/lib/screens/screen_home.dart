import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';

import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:ava_client/widgets/language_picker.dart';
import 'package:ava_client/localization/locales.dart';
import 'package:ava_client/screens/screen_auth.dart';
import 'package:ava_client/screens/documents/document_list.dart';
import 'package:ava_client/screens/tags/tags_list.dart';
import 'package:ava_client/screens/chat_history_screen.dart';

import 'package:ava_client/auth/secrets.dart' as sec;

class HomeScreen extends StatelessWidget {

  final ValueChanged<String> onLanguageChanged;
  const HomeScreen({super.key, required this.onLanguageChanged});

  Future<void> _logout(BuildContext context) async {
    try {
      await Supabase.instance.client.auth.signOut();
      
      sec.Tokens.accessToken = null;
      sec.Tokens.refreshToken = null;
      
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => AuthPage(onLanguageChanged: onLanguageChanged,)),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${LocaleData.logout_error.getString(context)}: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleData.title.getString(context)),
        actions: [
          // Language picker
          LanguageDropdown(
            initialLang: 'en',
            onChanged: onLanguageChanged,
          ),
          // Chat History
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ChatHistoryScreen()),
              );
            },
            icon: const Icon(Icons.chat_bubble_outline),
          ),
          // Tags
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const TagsWidget()),
              );
            },
            icon: const Icon(Icons.category_outlined),
          ),
          // Logout
          IconButton(
            onPressed: () => _logout(context),
            icon: const Icon(Icons.logout),
          ),
        ],
      ),
      body: DocumentsWidget()
      // bottomNavigationBar: null
    );
  }
}