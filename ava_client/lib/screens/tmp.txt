// Future<void> _uploadDocument() async {
  //   try {
  //     FilePickerResult? result = await FilePicker.platform.pickFiles(
  //       type: FileType.custom,
  //       allowedExtensions: ['pdf', 'txt', 'docx', 'doc', 'md', 'rtf', 'csv', 'json', 'html'],
  //     );

  //     if (result == null) return;

  //     final file = result.files.first;
  //     final userId = Supabase.instance.client.auth.currentUser?.id;
  //     if (userId == null) throw Exception('User not authenticated');

  //     final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.name}';
  //     final filePath = '$userId/$fileName';
      
  //     print('Uploading to path: $filePath');
  //     print('File size: ${file.size} bytes');
      
  //     Uint8List fileBytes;
  //     if (file.bytes != null) {
  //       fileBytes = file.bytes!;
  //     } else {
  //       fileBytes = await File(file.path!).readAsBytes();
  //     }

  //     print('File bytes length: ${fileBytes.length}');
      
  //     await Supabase.instance.client.storage
  //         .from('documents')
  //         .uploadBinary(filePath, fileBytes);
          
  //     print('Upload completed successfully');

  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(content: Text('Document uploaded successfully')),
  //     );
  //     _loadDocuments();
  //   } catch (e) {
  //     print('Upload error details: $e');
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       SnackBar(content: Text('Upload failed: $e')),
  //     );
  //   }
  // }
