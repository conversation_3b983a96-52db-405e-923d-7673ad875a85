
import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';

import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:ava_client/localization/locales.dart';
import 'package:ava_client/screens/screen_auth.dart';
import 'package:ava_client/screens/screen_home.dart';
import 'package:ava_client/services/voice/service_locator.dart';
import 'package:ava_client/services/voice/language_service.dart';
import 'package:ava_client/services/voice/voice_service.dart';

import 'package:ava_client/auth/secrets.dart' as sec;
// --------------------------------------------------------------------------------
Future<void> main() async{
  WidgetsFlutterBinding.ensureInitialized();      
  
  print('--> Initializing Localization...');
  await FlutterLocalization.instance.ensureInitialized();

  print('--> Initializing Services...');
  setupServiceLocator();
  await getIt<VoiceService>().initialize();

  print('--> Initializing Supabase connection...');
  await Supabase.initialize(
    url: sec.Secrets.supabaseUrl,
    anonKey: sec.Secrets.supabaseAnonKey
  );
  print('----> Supabase client: ${Supabase.instance.client}');
  
  runApp(const AvaApp());
}
// --------------------------------------------------------------------------------

class AvaApp extends StatefulWidget {
  const AvaApp({super.key});

  @override
  State<AvaApp> createState() => _AvaAppState();
}

class _AvaAppState extends State<AvaApp> {

  final FlutterLocalization localization = FlutterLocalization.instance;
  
  @override
  void initState() {
    _configureLocalization();
    super.initState();
  }

  void _configureLocalization() {
    localization.init(mapLocales: LOCALES, initLanguageCode: 'en');
    localization.onTranslatedLanguage = _onTranslatedLanguage;
  }

  void _onTranslatedLanguage(Locale? locale) {
    setState(() {});
  }

  void _onLanguageChanged(String languageCode) {
    print('Language changed to: $languageCode');
    localization.translate(languageCode);
    
    final voiceLanguage = switch (languageCode) {
      'sk' => SupportedLanguage.slovak,
      'hu' => SupportedLanguage.hungarian,
      _ => SupportedLanguage.english,
    };
    print('Mapped to voice language: ${voiceLanguage.code}');
    getIt<LanguageService>().setLanguage(voiceLanguage);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(      
      title: LocaleData.title.getString(context),
      supportedLocales: localization.supportedLocales,
      localizationsDelegates: localization.localizationsDelegates,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: Supabase.instance.client.auth.currentUser != null ?  HomeScreen(onLanguageChanged: _onLanguageChanged) : AuthPage(onLanguageChanged: _onLanguageChanged),
    );
  }
}
