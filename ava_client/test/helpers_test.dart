import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ava_client/main.dart';
import 'package:ava_client/screens/screen_home.dart';


Future<void> autoLogin(WidgetTester tester, {
  String testEmail = '<EMAIL>',
  String testPassword = 'abc12345!',
}) async {
  print("TTT -----> Testing autoLogin");
  await tester.pumpWidget(const AvaApp());
  await tester.pumpAndSettle();
  
  await tester.enterText(find.byType(TextField).first, testEmail);
  await tester.enterText(find.byType(TextField).last, testPassword);
  
  await tester.tap(find.byType(ElevatedButton));
  await tester.pumpAndSettle();
}

Future<void> goToHome(WidgetTester tester) async {
  print("Testing goToHome");
  await tester.pumpWidget(const MaterialApp(home: HomeScreen()));
  await tester.pumpAndSettle();
}

Future<void> autoLogout(WidgetTester tester) async {
  print("Testing autoLogout");  
  await tester.tap(find.byIcon(Icons.logout));
  await tester.pumpAndSettle();
}